# Taro 项目框架搭建

本项目使用 taro-cli 搭建完成,开发结构为 typescript+sass+vue3+vite

1、为了跨平台 Windows、macOS和Linux都可以正常运行，可以使用 `cross-env`包来统一设置环境变量，并修改package.json的脚本

```bash
pnpm install cross-env --save-dev
```

```json
{
  "scripts": {
    "dev:weapp": "cross-env NODE_ENV=production taro build --type weapp --watch",
    "build:weapp": "cross-env NODE_ENV=development taro build --type weapp"
  }
}
```

2、环境变量，我们根据types/global.d.ts 和 package.json的配置针对

// 这里可以获取到小程序的全局变量，比如 TARO_APP_API，对应在env.development 、env.production 中定义的变量

console.log("TARO_APP_API:", process.env.TARO_APP_API, process.env.TARO_APP_ID);

3、快捷页面生产方式

```
taro create 页面名称 --dir / # 主页面
taro create 页面名称 --subpkg mysubpages/针对性的模块，比如mine # 分包页面

```
