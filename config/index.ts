import { defineConfig, type UserConfigExport } from '@tarojs/cli';
import path from 'path';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import devConfig from './dev';
import prodConfig from './prod';
import NutUIResolver from '@nutui/auto-import-resolver';
import Components from 'unplugin-vue-components/webpack';

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig<'webpack5'>(async (merge, { command, mode }) => {
	const baseConfig: UserConfigExport<'webpack5'> = {
		projectName: 'taro_community',
		date: '2025-6-17',
		designWidth(input) {
			// 配置 NutUI 375 尺寸
			if (
				input &&
				typeof input === 'object' &&
				'file' in input &&
				typeof input.file === 'string' &&
				input.file.replace(/\\+/g, '/').indexOf('@nutui') > -1
			) {
				return 375;
			}
			// 全局使用 Taro 默认的 750 尺寸
			return 750;
		},
		deviceRatio: {
			640: 2.34 / 2,
			750: 1,
			375: 2,
			828: 1.81 / 2,
		},
		sourceRoot: 'src',
		outputRoot: 'dist',
		plugins: ['@tarojs/plugin-html'],
		defineConstants: {
			// Vue 3 特性标志
			__VUE_OPTIONS_API__: 'true',
			__VUE_PROD_DEVTOOLS__: 'false',
			__VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false',
		},
		copy: {
			patterns: [],
			options: {},
		},
		framework: 'vue3',
		compiler: {
			type: 'webpack5',
			prebundle: {
				enable: false,
			},
		},
		cache: {
			enable: true, // 开启 Webpack 持久化缓存
			buildDependencies: {
				config: [__filename], // 当配置文件改变时，缓存失效
			},
		},
		mini: {
			postcss: {
				pxtransform: {
					enable: true,
					config: {},
				},
				cssModules: {
					enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
					config: {
						namingPattern: 'module', // 转换模式，取值为 global/module
						generateScopedName: '[name]__[local]___[hash:base64:5]',
					},
				},
			},
			webpackChain(chain) {
				chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin);
				chain.plugin('unplugin-vue-components').use(
					Components({
						resolvers: [NutUIResolver({ taro: true })],
					}),
				);
			},
		},
		h5: {
			publicPath: '/',
			staticDirectory: 'static',
			devServer: {
				headers: {
					'Cross-Origin-Opener-Policy': 'same-origin',
					'Cross-Origin-Embedder-Policy': 'require-corp',
				},
			},
			output: {
				filename: 'js/[name].[hash:8].js',
				chunkFilename: 'js/[name].[chunkhash:8].js',
			},
			miniCssExtractPluginOption: {
				ignoreOrder: true,
				filename: 'css/[name].[hash].css',
				chunkFilename: 'css/[name].[chunkhash].css',
			},
			postcss: {
				autoprefixer: {
					enable: true,
					config: {},
				},
				cssModules: {
					enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
					config: {
						namingPattern: 'module', // 转换模式，取值为 global/module
						generateScopedName: '[name]__[local]___[hash:base64:5]',
					},
				},
			},
			webpackChain(chain) {
				chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin);
				chain.plugin('unplugin-vue-components').use(
					Components({
						resolvers: [NutUIResolver({ taro: true })],
					}),
				);
			},
		},
		rn: {
			appName: 'taroDemo',
			postcss: {
				cssModules: {
					enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
				},
			},
		},
		alias: {
			'@': path.resolve(__dirname, '..', 'src'),
			'@/components': path.resolve(__dirname, '..', 'src/components'),
			'@/utils': path.resolve(__dirname, '..', 'src/utils'),
			'@/package': path.resolve(__dirname, '..', 'package.json'),
			'@/project': path.resolve(__dirname, '..', 'project.config.json'),
		},
	};
	if (process.env.NODE_ENV === 'development') {
		// 本地开发构建配置（不混淆压缩）
		return merge({}, baseConfig, devConfig);
	}
	// 生产构建配置（默认开启压缩混淆等）
	return merge({}, baseConfig, prodConfig);
});
