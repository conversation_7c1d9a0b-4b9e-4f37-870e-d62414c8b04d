# Taro Community 园区项目说明文档

## 项目概述

**项目名称**：taro_community  
**项目描述**：园区项目微信小程序  
**版本**：1.0.0  
**开发框架**：Taro 4.1.2  

这是一个基于 Taro 框架开发的微信小程序，主要服务于园区管理和社区服务，提供便民服务、消息通知、个人中心等功能。

## 技术栈

### 核心技术
- **框架**：Taro 4.1.2
- **前端框架**：Vue 3.3.0
- **开发语言**：TypeScript 5.1.0
- **样式预处理**：Sass/SCSS
- **构建工具**：Webpack 5.91.0

### 主要依赖
- **UI 组件库**：@nutui/nutui-taro 4.2.8
- **图标库**：@nutui/icons-vue-taro 0.0.9
- **状态管理**：Pinia 3.0.3
- **代码规范**：ESLint + Prettier
- **Git 钩子**：Husky + lint-staged

## 项目结构

```
taro_community/
├── config/                    # 构建配置
│   ├── dev.ts                # 开发环境配置
│   ├── prod.ts               # 生产环境配置
│   └── index.ts              # 主配置文件
├── src/
│   ├── api/                  # API 接口定义
│   │   ├── auth.ts           # 认证相关接口
│   │   ├── common.ts         # 通用接口
│   │   └── index.ts          # 接口统一导出
│   ├── assets/               # 静态资源
│   │   ├── images/           # 图片资源
│   │   └── tab/              # Tab 图标
│   ├── components/           # 公共组件
│   │   ├── CustomNavBar/     # 自定义导航栏
│   │   └── CustomModal/      # 自定义弹窗
│   ├── hooks/                # 自定义 Hooks
│   │   └── useLifecycle.ts   # 生命周期管理
│   ├── libs/                 # 工具库
│   │   ├── request.ts        # 网络请求封装
│   │   └── request-setup.ts  # 请求初始化
│   ├── pages/                # 主包页面
│   │   ├── index/            # 首页
│   │   ├── message/          # 消息页
│   │   └── mine/             # 我的页面
│   ├── pagesA/               # 分包页面
│   │   ├── enterpriseAuth/   # 企业认证
│   │   ├── identityAuth/     # 身份认证
│   │   └── profile/          # 个人资料
│   ├── stores/               # 状态管理
│   ├── styles/               # 样式文件
│   ├── utils/                # 工具函数
│   ├── app.config.ts         # 应用配置
│   ├── app.ts                # 应用入口
│   └── app.scss              # 全局样式
├── types/                    # 类型定义
├── package.json              # 项目依赖
└── tsconfig.json             # TypeScript 配置
```

## 功能模块

### 1. 首页模块 (`pages/index`)
- **轮播图展示**：支持自动播放的图片轮播
- **区域选择**：支持下拉选择不同园区楼栋
- **浮动按钮**：快速访问门禁功能
- **登录状态监听**：实时响应用户登录状态变化

### 2. 消息模块 (`pages/message`)
- **消息通知**：接收园区相关通知
- **消息分类**：支持不同类型消息展示

### 3. 个人中心 (`pages/mine`)
- **用户信息展示**
- **功能入口**：账单、合同、维修等功能访问

### 4. 认证模块 (`pagesA`)
- **企业认证**：企业用户身份认证
- **个人认证**：个人用户身份认证
- **个人资料**：用户信息管理

## 核心特性

### 1. 状态管理 (Pinia)
```typescript
// 用户状态管理
const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
  }),
  actions: {
    setUserInfo(userInfo: any) {
      this.userInfo = userInfo;
    },
    isLogin() {
      return this.userInfo !== null;
    },
  },
});
```

### 2. 网络请求封装
- **拦截器机制**：请求/响应拦截器
- **认证处理**：自动添加 token
- **错误处理**：统一错误处理和提示
- **加载状态**：自动管理加载提示

### 3. 屏幕适配
- **自定义导航栏**：适配不同机型
- **响应式设计**：支持多种屏幕尺寸
- **rpx 单位**：自动转换适配

### 4. 生命周期管理
```typescript
const { pageState, isLoading, setLoading } = useLifecycle({
  onLoad: async (options) => {
    // 页面加载逻辑
  },
  onShow: async () => {
    // 页面显示逻辑
  }
});
```

## 开发指南

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- 微信开发者工具

### 安装依赖
```bash
npm install
```

### 开发命令
```bash
# 微信小程序开发
npm run dev:weapp

# 微信小程序生产构建
npm run build:weapp

# H5 开发
npm run dev:h5

# 其他平台
npm run dev:swan      # 百度小程序
npm run dev:alipay    # 支付宝小程序
npm run dev:tt        # 字节跳动小程序
```

### 代码规范
项目集成了 ESLint + Prettier 代码规范：

```bash
# 检查代码规范
npm run lint

# 自动修复代码格式
npm run format
```

### Git 提交规范
项目使用 Husky + lint-staged 确保代码质量：
- 提交前自动执行代码格式化
- 提交前进行 ESLint 检查

## 配置说明

### 应用配置 (`app.config.ts`)
- **页面路由**：定义主包和分包页面
- **底部 Tab**：配置导航栏样式和页面
- **窗口配置**：设置导航栏样式
- **网络超时**：配置请求超时时间

### 构建配置 (`config/index.ts`)
- **多端适配**：支持微信、H5、百度等多端
- **NutUI 集成**：自动导入组件
- **TypeScript 路径映射**
- **Webpack 优化配置**

## API 接口

### 认证接口 (`api/auth.ts`)
- 用户登录
- 微信授权登录
- 手机号登录
- 用户信息获取

### 通用接口 (`api/common.ts`)
- 分页查询
- 文件上传
- 系统配置

## 部署说明

### 微信小程序部署
1. 执行构建命令：`npm run build:weapp`
2. 在微信开发者工具中打开 `dist` 目录
3. 点击"上传"按钮上传代码
4. 在微信公众平台提交审核

### H5 部署
1. 执行构建命令：`npm run build:h5`
2. 将 `dist` 目录部署到服务器
3. 配置 nginx 或其他 web 服务器

## 开发规范

### 目录命名
- 使用小写字母和连字符
- 组件目录使用 PascalCase

### 文件命名
- 页面文件：`index.vue`
- 样式文件：`index.scss`
- 配置文件：`index.config.ts`

### 代码规范
- 使用 TypeScript 进行类型定义
- 组件使用 Composition API
- 状态管理使用 Pinia
- 接口请求统一使用封装的 request 库

## 常见问题

### 1. 如何添加新页面？
1. 在 `src/pages` 或 `src/pagesA` 创建页面目录
2. 在 `app.config.ts` 中添加页面路径
3. 创建 `index.vue`、`index.scss`、`index.config.ts` 文件

### 2. 如何使用自定义组件？
```vue
<template>
  <CustomNavBar title="页面标题">
    <template #left>
      <!-- 自定义左侧内容 -->
    </template>
  </CustomNavBar>
</template>
```

### 3. 如何进行状态管理？
```typescript
import { useUserStore } from '@/stores';

const userStore = useUserStore();
const isLogin = computed(() => userStore.isLogin());
```

## 更新日志

### v1.0.0 (2025-06-17)
- 初始版本发布
- 实现基础页面结构
- 集成 NutUI 组件库
- 完成用户认证功能
- 添加网络请求封装

---

**维护团队**：前端开发团队  
**最后更新**：2025-06-17 