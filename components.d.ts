/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CustomModal: typeof import('./src/components/CustomModal/CustomModal.vue')['default']
    CustomNavBar: typeof import('./src/components/CustomNavBar/CustomNavBar.vue')['default']
    NutButton: typeof import('@nutui/nutui-taro')['Button']
    NutInput: typeof import('@nutui/nutui-taro')['Input']
    NutPicker: typeof import('@nutui/nutui-taro')['Picker']
    NutPopup: typeof import('@nutui/nutui-taro')['Popup']
    NutSwiper: typeof import('@nutui/nutui-taro')['Swiper']
    NutSwiperItem: typeof import('@nutui/nutui-taro')['SwiperItem']
    NutTag: typeof import('@nutui/nutui-taro')['Tag']
  }
}
