import Taro from '@tarojs/taro';

// 注意几个概念
// 胶囊宽度：menuButtonInfo.width
// 胶囊左侧距离屏幕左侧的间距：menuButtonInfo.left
// 胶囊右侧距离屏幕左侧的间距：menuButtonInfo.right
// 胶囊顶部距离屏幕顶部的间距：menuButtonInfo.top
// 胶囊底部距离屏幕顶部的间距：menuButtonInfo.bottom
// 屏幕宽度：windowInfo.windowWidth
// 屏幕高度：windowInfo.windowHeight
// 状态栏高度：windowInfo.statusBarHeight

// 获取窗口信息（替代 getSystemInfoSync）
const windowInfo = Taro.getWindowInfo();
// 获取设备信息
const deviceInfo = Taro.getDeviceInfo();
// 获取应用基础信息
const appBaseInfo = Taro.getAppBaseInfo();

// 胶囊按钮位置信息
const menuButtonInfo: Taro.getMenuButtonBoundingClientRect.Rect =
	Taro.getMenuButtonBoundingClientRect();

// 导航栏高度 = 状态栏到胶囊的间距（胶囊上坐标位置-状态栏高度） * 2 + 胶囊高度 + 状态栏高度
export const getNavBarHeight = (): number => {
	if (windowInfo.statusBarHeight && menuButtonInfo.height) {
		return (
			(menuButtonInfo.top - windowInfo.statusBarHeight) * 2 +
			menuButtonInfo.height +
			windowInfo.statusBarHeight
		);
	}
	// 提供默认值，避免返回 undefined
	return 44; // 默认导航栏高度
};

// 状态栏和菜单按钮(标题栏)之间的间距
// 等同于菜单按钮(标题栏)到正文之间的间距（胶囊上坐标位置-状态栏高度）
export const getMenuBottom = (): number => {
	if (windowInfo.statusBarHeight && menuButtonInfo.top) {
		return menuButtonInfo.top - windowInfo.statusBarHeight;
	}
	// 提供默认值，避免返回 undefined
	return 6; // 默认间距
};

// 菜单按钮栏(标题栏)的高度
export const getMenuHeight = (): number => {
	return menuButtonInfo.height || 32; // 提供默认值，避免返回 undefined
};

// 导航栏的正文内容宽度 = 屏幕宽度 - 胶囊宽度 - 胶囊右侧距离右边屏幕的距离 - 设置一点距离
export const getNavBarContentWidth = () => {
	return (
		windowInfo.windowWidth -
		menuButtonInfo.width -
		(windowInfo.windowWidth - menuButtonInfo.right) -
		30
	);
};

// 获取高度 - 通过比例参数
export const getHeightByRatio = (ratio: number) => {
	return windowInfo.windowWidth / ratio;
};
