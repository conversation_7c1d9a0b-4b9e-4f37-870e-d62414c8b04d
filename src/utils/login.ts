import Taro from '@tarojs/taro';
import {
	jwCloudAuth_SecurityCenter_Login_POST,
	jwCloudAuth_SecurityCenter_System_User_GetWebCurrentUserInfo_GET,
	jwCloudAuth_SecurityCenter_Quit_POST,
} from './api';
import { useAuthStore, useUserStore } from '@/stores';
import { saveStore } from '@/utils/utils';

// 微信手机号登录
export const onLoginHandler = async (username: string, password: string) => {
	// 初始化一个token字符串
	let token = '';
	try {
		const params = {
			appCode: 'WEB',
			username: username,
			password: password,
		};
		// 通过调用 后端与微信平台对接的接口来判断 用户是直接存在的还是需要新注册
		const loginRes: any = await jwCloudAuth_SecurityCenter_Login_POST(params);
		token = loginRes;
		// 登录成功后，需要存储两遍
		// 1.存储到LocalStorage中，使用saveStore方法
		// 2.存储到pinia中，使用useAuthStore方法
		// 这样的好处是避免频繁读写LocalStorage
		// 3.刷新或者重新打开小程序时，读取LocalStorage的数据赋值到pinia中
		saveStore('token', token);
		const authStore = useAuthStore();
		authStore.setToken(token);
		// 登录成功后，将token存储到本地
		const userInfoRes =
			await jwCloudAuth_SecurityCenter_System_User_GetWebCurrentUserInfo_GET();
		const userStore = useUserStore();
		userStore.setUserInfo(userInfoRes);
		saveStore('userInfo', userInfoRes);
		Taro.showToast({
			title: '登录成功',
			icon: 'success',
		});

		// 跳转到首页
		setTimeout(() => {
			Taro.reLaunch({
				url: '/pages/index/index',
			});
		}, 500);
	} catch (error) {
		console.log('login error:', error);
		Taro.showToast({
			title: error.errMsg,
			icon: 'none',
		});
	}
};

// 退出登录
export const onLogoutHandler = async () => {
	try {
		// 退出登录
		await jwCloudAuth_SecurityCenter_Quit_POST();
	} catch (error) {
		Taro.showToast({
			title: '退出登录失败',
			icon: 'none',
		});
	} finally {
		const authStore = useAuthStore();
		authStore.setToken('');
		const userStore = useUserStore();
		userStore.setUserInfo(null);
		saveStore('token', '');
		saveStore('userInfo', null);
		Taro.reLaunch({ url: '/pages/login/index' });
	}
};
