import { showToast } from '@tarojs/taro';
export const checkUser = (user) => {
	if (!user) {
		showToast({
			title: '请输入用户名',
			icon: 'none',
		});
		return false;
	}
	return true;
};
export const checkPassword = (password) => {
	if (!password) {
		showToast({
			title: '请输入密码',
			icon: 'none',
		});
		return false;
	}
	return true;
};
export const checkRepeatPassword = (password) => {
	if (!password) {
		showToast({
			title: '请输入密码',
			icon: 'none',
		});
		return false;
	} else if (password.length < 5) {
		showToast({
			title: '密码请大于五位',
			icon: 'none',
		});
	}
	return true;
};

//告警相关检查
const execMaxColor = 'red';
const lowerMinColor = '#ffcc33';
const normalColor = '#61d788';
/**
 * 用于传感器数值比对返回相应颜色
 * 校验用方法,需要传入value,如果不存在max和min则返回normalColor
 * 超过返回execMaxColor
 * 低于返回lowerMinColor
 * @param {number} value
 * @param {max,min} Object
 */
export const validateMonitorColor = (
	value?,
	option = { max: NaN || undefined, min: NaN || undefined },
) => {
	const { max, min } = option;
	if (max && value > max) return execMaxColor;
	if (min && value < min) return lowerMinColor;
	return normalColor;
};
