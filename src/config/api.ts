/**
 * API 配置文件
 * 管理不同环境的 API 地址和配置
 */

// 环境类型
export type Environment = 'development' | 'production';

// API 配置接口
export interface ApiConfig {
	baseURL: string;
	timeout: number;
	enableMock: boolean;
	enableLog: boolean;
}

// 不同环境的配置
const configs: Record<Environment, ApiConfig> = {
	// 开发环境
	development: {
		baseURL: 'http://10.151.0.71',
		timeout: 10000,
		enableMock: true,
		enableLog: true,
	},

	// 生产环境
	production: {
		baseURL: 'http://10.151.0.71',
		timeout: 8000,
		enableMock: false,
		enableLog: false,
	},
};

/**
 * 获取当前环境
 */
export const getCurrentEnv = (): Environment => {
	// 可以通过环境变量或其他方式判断
	if (process.env.NODE_ENV === 'production') {
		return 'production';
	}
	return 'development';
};

/**
 * 获取当前环境配置
 */
export const getApiConfig = (): ApiConfig => {
	const env = getCurrentEnv();
	return configs[env];
};

/**
 * API 路径常量
 */
export const API_PATHS = {
	// 认证相关
	AUTH: {
		LOGIN: '/auth/login',
		LOGOUT: '/auth/logout',
		REFRESH_TOKEN: '/auth/refresh',
		WECHAT_LOGIN: '/auth/wechat/login',
		PHONE_LOGIN: '/auth/phone/login',
	},

	// 用户相关
	USER: {
		PROFILE: '/user/profile',
		UPDATE_PROFILE: '/user/profile',
		AVATAR_UPLOAD: '/user/avatar',
		CHANGE_PASSWORD: '/user/password',
	},

	// 业务相关
	BUSINESS: {
		LIST: '/business/list',
		DETAIL: '/business/detail',
		CREATE: '/business/create',
		UPDATE: '/business/update',
		DELETE: '/business/delete',
	},

	// 文件上传
	UPLOAD: {
		IMAGE: '/upload/image',
		FILE: '/upload/file',
	},

	// 系统相关
	SYSTEM: {
		CONFIG: '/system/config',
		VERSION: '/system/version',
	},
} as const;

/**
 * 响应状态码
 */
export const RESPONSE_CODE = {
	SUCCESS: 0,
	ERROR: -1,
	UNAUTHORIZED: 401,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	SERVER_ERROR: 500,
} as const;

/**
 * 请求头常量
 */
export const HEADERS = {
	CONTENT_TYPE: {
		JSON: 'application/json',
		FORM: 'application/x-www-form-urlencoded',
		MULTIPART: 'multipart/form-data',
	},
	AUTHORIZATION: 'Authorization',
	PLATFORM: 'X-Platform',
	VERSION: 'X-Version',
} as const;
