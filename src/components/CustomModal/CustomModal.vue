<template>
	<view v-if="visible" class="modal-mask">
		<view class="modal-container">
			<!-- 标题 -->
			<view v-if="title" class="modal-title">{{ title }}</view>

			<!-- 内容 -->
			<view class="modal-content">
				<slot>{{ content }}</slot>
			</view>

			<!-- 底部按钮 -->
			<view class="modal-footer">
				<button v-if="showCancel" class="modal-btn cancel-btn" @click="handleCancel">
					{{ cancelText }}
				</button>
				<button
					v-if="isPhoneAuth"
					class="modal-btn confirm-btn"
					open-type="getPhoneNumber"
					@getPhoneNumber="handleConfirm"
					:style="confirmButtonStyle"
				>
					{{ confirmText }}
				</button>
				<button
					v-else
					class="modal-btn confirm-btn"
					@click="handleConfirm"
					:style="confirmButtonStyle"
				>
					{{ confirmText }}
				</button>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({
	name: 'CustomModal',
});

const props = defineProps({
	visible: {
		type: Boolean,
		default: false,
	},
	title: String,
	content: String,
	confirmText: {
		type: String,
		default: '确定',
	},
	cancelText: {
		type: String,
		default: '取消',
	},
	showCancel: {
		type: Boolean,
		default: true,
	},
	confirmButtonStyle: {
		type: Object,
		default: () => ({}),
	},
	isPhoneAuth: {
		type: Boolean,
		default: false,
	},
});

const emit = defineEmits(['confirm', 'cancel', 'update:visible']);

const handleConfirm = (e) => {
	emit('update:visible', false);
	emit('confirm', props.isPhoneAuth ? e : null);
};

const handleCancel = () => {
	emit('cancel');
	emit('update:visible', false);
};
</script>

<style lang="scss">
.modal-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999; // 提高到比tabbar更高的层级
}

.modal-container {
	width: 80%;
	background-color: #fff;
	border-radius: 20px;
	overflow: hidden;
}

.modal-title {
	padding: 40px 40px 0px 40px;
	font-size: 32px;
	font-weight: bold;
	text-align: center;
}

.modal-content {
	padding: 40px 60px 60px 60px;
	font-size: 32px;
	color: #666;
	text-align: center;
}

.modal-footer {
	display: flex;
	border-top: 1px solid #f5f6f7;
}

.modal-btn {
	flex: 1;
	height: 96px;
	line-height: 96px;
	font-size: 32px;
	background: transparent;
	border: none;
	border-radius: 0;

	&::after {
		border: none;
	}
}

.cancel-btn {
	color: #666;
	border-right: 1px solid #f5f6f7;
}

.confirm-btn {
	color: #397fef;
	font-weight: 700;
}
</style>
