<template>
	<view class="nav" :style="{ height: `${navBarHeight}px` }">
		<!-- 胶囊区域 -->
		<view
			class="capsule_box"
			:style="{
				height: `${menuHeight}px`,
				minHeight: `${menuHeight}px`,
				lineHeight: `${menuHeight}px`,
				bottom: `${menuBottom}px`,
			}"
		>
			<view class="nav_left">
				<!-- 返回 -->
				<slot name="left"></slot>
			</view>
			<view class="nav_title">{{ title }}</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { getNavBarHeight, getMenuBottom, getMenuHeight } from '@/utils/screen';

// 定义组件名称
defineOptions({
	name: 'CustomNavBar',
});

// 定义 props
interface Props {
	title?: string;
}

withDefaults(defineProps<Props>(), {
	title: '',
});

// 计算属性
const navBarHeight = computed(() => getNavBarHeight());
const menuHeight = computed(() => getMenuHeight());
const menuBottom = computed(() => getMenuBottom());
</script>

<style lang="scss">
.nav {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: #fff;
	border-bottom: 1px solid #f0f0f0;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	// 胶囊栏
	.capsule_box {
		position: absolute;
		left: 0;
		right: 0;
		display: flex;
		justify-content: center;
		align-items: center;

		.nav_left {
			position: absolute;
			left: 30px;
			display: flex;
			align-items: center;
		}
		// 标题文字
		.nav_title {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			text-align: center;
			height: 100%;
			width: 50%;
			margin: 0 auto;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			font-size: 36px;
			font-weight: bold;
		}
	}
}
</style>
