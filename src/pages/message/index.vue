<template>
	<view class="message">
		<view class="body-card">
			<view
				class="body-card-cell"
				v-for="(item, index) in messageList"
				:key="index"
				@click="onCellHandler(item)"
			>
				<view class="body-card-cell-left">
					<image :src="item.icon" class="body-card-cell-left-img" />
					<text>{{ item.title }}</text>
				</view>
				<view class="body-card-cell-right"></view>
			</view>
		</view>
	</view>

	<CustomModal
		:is-phone-auth="true"
		v-model:visible="modalVisible"
		:title="modalConfig.title"
		:content="modalConfig.content"
		:confirm-text="modalConfig.confirmText"
		:cancel-text="modalConfig.cancelText"
		@confirm="onConfirm"
	/>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import './index.scss';
import Taro from '@tarojs/taro';
import { onLoginHandler } from '@/utils/login';
import { useUserStore } from '@/stores';
import MessageNoticePNG from '@/assets/images/message_notice.png';
import MessagePublishPNG from '@/assets/images/message_publish.png';
import MessageParkPNG from '@/assets/images/message_park.png';
const userStore = useUserStore();
const isLogin = computed(() => userStore.isLogin());
// 在这里监听watch的时候重新渲染界面即可
watch(isLogin, (newValue, oldValue) => {
	if (newValue && !oldValue) {
		// 用户刚刚登录成功
		console.log('用户登录成功，【消息页面】刷新页面数据');
		// 可以在这里执行需要登录后才能获取的数据
		// 比如：获取用户专属内容、刷新权限相关的UI等
	} else if (!newValue && oldValue) {
		// 用户刚刚退出登录
		console.log('用户退出登录，【消息页面】清理页面数据');
		// 清理需要登录才能显示的数据
	}
});

onMounted(() => {
	console.log('mounted 消息初始化：', isLogin.value);
});
const modalVisible = ref(false);
const modalConfig = ref({
	title: '手机号快速登录',
	content: '使用微信手机号进行快速登录，享受更好的服务体验',
	confirmText: '立即授权',
	cancelText: '取消',
});

// 消息列表
const messageList = [
	{
		title: '消息提醒',
		icon: MessageNoticePNG,
		path: '',
	},
	{
		title: '公告通知',
		icon: MessagePublishPNG,
		path: '',
	},
	{
		title: '园区公告',
		icon: MessageParkPNG,
		path: '',
	},
];

const onCellHandler = (item) => {
	if (!isLogin.value) {
		modalVisible.value = true;
		return;
	}
	Taro.showToast({
		title: `${item.title}功能正在开发中...`,
		icon: 'none',
	});
};

const onConfirm = (e: any) => {
	console.log('点击了确定:', e);
	// 处理确认逻辑
	onLoginHandler(e, '/pages/message/index');
};
</script>
