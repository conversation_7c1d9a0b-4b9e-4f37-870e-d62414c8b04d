.login-container {
	min-height: 100vh;
	background: #ffffff;
	display: flex;
	flex-direction: column;
	padding: 40px;
	box-sizing: border-box;
}

.login-form {
	width: 100%;
	background: transparent;
	padding: 0;
}

.form-item {
	margin-bottom: 24px;
  display: flex;
  align-items: center;
  image {
    width: 30px;
    height: 30px;
    margin-right: 10px;
  }

	&:last-child {
		margin-bottom: 0;
	}
}


.login-btn-container {
	margin-top: 60px;
}

.login-btn {
	width: 100%;
	height: 80px;
	background: #397fef;
	border: none;
	border-radius: 10px;
	font-size: 30px;
	font-weight: 600;
	color: #ffffff;
	transition: all 0.3s ease;

	&:hover {
		background: #2968d8;
	}

	&:active {
		transform: translateY(1px);
		box-shadow: 0 2px 8px rgba(57, 127, 239, 0.3);
	}

	&.nut-button--loading {
		opacity: 0.8;
	}
}

// 覆盖NutUI组件样式
.nut-input {
	background: transparent !important;
	border: none !important;
	border-bottom: 2px solid #e9ecef !important;
	border-radius: 0 !important;
	padding: 20px 0;
	margin: 20px 0;

	.nut-input-value {
		background: transparent !important;
		border: none !important;
		font-size: 16px !important;
		color: #333333 !important;
		padding: 0 0 8px 0 !important;

		&::placeholder {
			color: #999999 !important;
		}
	}

	&:focus-within {
		border-bottom-color: #397fef !important;
		box-shadow: none !important;
	}
}

// nut的input输入框
.input-text {
	color: #333;
	font-size: 36px;
}

.nut-button {
	border: none !important;

	&.nut-button--primary {
		background: #397fef !important;

		&:hover {
			background: #2968d8 !important;
		}
	}
}
