.mine {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;

	// 渐变背景头部区域
	.gradient-header {
		width: 100%;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-start;
		box-sizing: border-box;
		padding: 0 30px;

		// 以 #397fef 为主的蓝色渐变背景
		background: linear-gradient(
			135deg,
			#2968d4 0%,
			// 浅蓝色（左上）
			#397fef 35%,
			// 中浅蓝色
			#5ca0ff 60%,
			// 中蓝色
			#7fb8ff 95%,
			// 主色调蓝色
			#a8d5ff 100% // 深蓝色（右下）
		);

		// 个人中心文字样式
		.custom-nav-bar {
			width: 100%;
			font-size: 36px;
			font-weight: bold;
			color: #fff;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
			letter-spacing: 2px;
			.center-title {
				margin-left: 30px;
			}
		}
		// 用户信息卡片
		.user-card {
			width: 100%;
			border-radius: 12px;
			padding: 60px 0px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 20px;

			.user-avatar {
				width: 120px;
				height: 120px;
				border-radius: 50%;
				overflow: hidden;
				flex-shrink: 0; // 防止头像被压缩
				.avatar-img {
					width: 100%;
					height: 100%;
				}
			}
			.user-info {
				flex: 1;
				margin-left: 30px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				gap: 10px;
				.user-name {
					font-size: 32px;
					font-weight: bold;
					color: #fff;
				}
				.user-phone {
					font-size: 24px;
					color: #d4d4d8;
				}
			}
			// 未实名认证提示
			.user-id-card {
				font-size: 24px;
				color: #ffcc00; // 改为黄色文字
				font-weight: 600;
				text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
			}

			// 箭头图标样式
			.arrow-icon {
				padding: 60px 0px 60px 60px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	// 线性的card
	.line-card {
		height: 140px;
		background: #fff;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(100, 100, 100, 0.3);
		margin: 0 30px;
		position: relative;
		top: -50px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30px;
		.line-card-item {
			width: 25%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			gap: 10px;
			.line-card-item-icon {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 50px;
				height: 50px;
			}
			.line-card-item-text {
				font-size: 24px;
				font-weight: 600;
				color: #333;
			}
		}
	}

	.mine-content {
		flex: 1;
		overflow-y: auto;
		flex-direction: column;
	}

	// 内容区域
	.body-card {
		background: #fff;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(100, 100, 100, 0.3);
		margin: 0px 30px 30px 30px;
		position: relative;
		padding: 30px;
		.body-card-title {
			font-size: 36px;
			font-weight: 600;
			color: #333;
			margin-bottom: 20px;
		}
		.body-card-content {
			display: flex;
			align-items: center;
			flex-direction: column;
			.body-card-cell {
				width: 100%;
				border-bottom: 1px solid #f5f6f7;
				padding: 24px 0;
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 30px;
				color: #333;
				.body-card-cell-left {
					display: flex;
					align-items: center;
					gap: 15px;
					.body-card-cell-left-img {
						width: 54px;
						height: 54px;
					}
				}
			}
		}
	}

	.taro-button-plain {
		background: transparent !important;
		border: none !important;
		margin: 0 !important;
		padding: 0 !important;
		outline: 'none' !important;
	}

	.text-button {
		background: transparent;
		border: none;
		margin-left: 30px;

		&::after {
			display: none; /* 隐藏按钮默认边框 */
		}

		.button-text {
			font-size: 32px;
			font-weight: bold;
			color: #fff;
		}
	}
}
