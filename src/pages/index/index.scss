.index {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	// 渐变背景头部区域
	.gradient-header {
		width: 100%;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-start;
		box-sizing: border-box;
		padding: 0 30px;

		// 以 #397fef 为主的蓝色渐变背景
		background: linear-gradient(
			135deg,
			#2968d4 0%,
			// 浅蓝色（左上）
			#397fef 35%,
			// 中浅蓝色
			#5ca0ff 60%,
			// 中蓝色
			#7fb8ff 95%,
			// 主色调蓝色
			#a8d5ff 100% // 深蓝色（右下）
		);

		// 个人中心文字样式
		.custom-nav-bar {
			width: 100%;
			font-size: 36px;
			color: #fff;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
			letter-spacing: 2px;
			.center-title {
				text-align: center;
			}
		}
		.user-info-content {
			width: 100%;
			// padding: 30px 0px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 10px;
			.user-role {
				flex: 1;
				margin-right: 20px;
				display: flex;
				flex-direction: column;
				gap: 10px;
				.user-role-text {
					font-size: 40px;
					font-weight: bold;
					color: #fff;
				}
				.user-role-tags {
					color: #fff;
					display: flex;
					flex-wrap: wrap;
					gap: 7px;
				}
			}
			.user-avatar {
				width: 120px;
				height: 120px;
				border-radius: 50%;
				overflow: hidden;
				flex-shrink: 0; // 防止头像被压缩
				.avatar-img {
					width: 100%;
					height: 100%;
				}
			}
		}
		.user-info-footer {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 15px 0;
			box-sizing: border-box;
			.footer-area {
				flex: 1;
				display: flex;
				align-items: center;
				gap: 15px;
				color: #fff;
				margin-right: 20px;
			}
			.footer-logout {
				color: #fff;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0 10px;
				image {
					width: 60px;
					height: 60px;
				}
			}
		}
	}
}
