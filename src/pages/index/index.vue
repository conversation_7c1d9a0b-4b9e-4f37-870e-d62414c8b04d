<template>
  <view class="index">
    <view class="gradient-header">
      <view class="custom-nav-bar" :style="{ height: `${getNavBarHeight()}px` }">
        <view class="center-title" :style="{ marginTop: `${getNavBarHeight() - getMenuHeight()}px` }">
          个人中心
        </view>
      </view>
      <view class="user-info-content">
        <view class="user-role">
          <text class="user-role-text">你好，{{ userInfo?.trueName || '匿名用户' }}</text>
          <view class="user-role-tags">
            <nut-tag type="warning"> 普通用户1 </nut-tag>
            <nut-tag type="warning"> 普通用户2 </nut-tag>
          </view>
        </view>
        <view class="user-avatar">
          <image class="avatar-img" src="@/assets/images/mine_avatar.png" mode="widthFix" />
        </view>
      </view>
      <view class="user-info-footer">
        <view class="footer-area">
          <TriangleDown color="white" size="15" />
          <text>选择区域</text>
        </view>
        <view class="footer-logout" @click="onLogoutHandler">
          <image src="@/assets/images/logout.png" alt="" mode="heightFix" />
        </view>
      </view>
    </view>
  </view>

</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import './index.scss';
import { getNavBarHeight, getMenuHeight } from '@/utils/screen';
import { useUserStore } from '@/stores';
import { onLogoutHandler } from '@/utils/login';

// 引入图标组件
import { TriangleDown } from '@nutui/icons-vue-taro';
const userStore = useUserStore();
const userInfo = computed(() => userStore.getUserInfo());





</script>
