.profile {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #f5f5f5;
	.profile-body {
    background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 100px 0;
		.profile-header {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 80px;
			gap: 5px;
			.profile-avatar {
				width: 180px;
				height: 180px;
				border-radius: 50%;
				overflow: hidden;
				flex-shrink: 0; // 防止头像被压缩
				.avatar-img {
					width: 100%;
					height: 100%;
				}
			}
			.profile-avatar-text {
				font-size: 24px;
				color: grey;
			}
		}
		.profile-info {
			width: 100%;
			display: flex;
			align-items: center;
			flex-direction: column;
			padding: 0 30px;
			box-sizing: border-box;
			background-color: #fff;
			.profile-info-cell {
				width: 100%;
				border-bottom: 1px solid #f5f6f7;
				margin: 0 30px;
				padding: 30px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 20px;
				font-size: 32px;
				color: #333;
				.profile-info-cell-left {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					.profile-info-cell-label {
						width: 180px;
					}
					.profile-info-cell-value {
						flex: 1;
						color: grey;
					}
				}
				.profile-info-cell-right {
					color: #999;
					font-size: 24px;
				}
			}
		}
	}

	.taro-button-plain {
		background: transparent !important;
		border: none !important;
		margin: 0 !important;
		padding: 0 !important;
		outline: 'none' !important;
	}
	// nut的input输入框
	.nut-input {
		padding: 0;
		margin: 0;
	}
	.input-text {
		color: grey;
		font-size: 32px;
	}
}
