<template>
	<view class="profile">
		<view class="profile-body">
			<view class="profile-header">
				<view class="profile-avatar">
					<Button
						open-type="chooseAvatar"
						@chooseAvatar="onChooseAvatar"
						class="taro-button-plain"
					>
						<!-- 帮我加个图片加载失败时候的默认图片，图片渲染失败了我也想用默认图片，默认图片的路径是DefaultAvatar -->
						<image
							:src="avatarUrl"
							class="avatar-img"
							mode="widthFix"
							:default-src="DefaultAvatar"
							@error="onAvatarError"
						/>
					</Button>
				</view>
				<text class="profile-avatar-text">点击修改头像</text>
			</view>
			<view class="profile-info">
				<view class="profile-info-cell">
					<view class="profile-info-cell-left">
						<text class="profile-info-cell-label">昵称</text>
						<view class="profile-info-cell-value">
							<NutInput
								v-model="nicknameStr"
								placeholder="请输入昵称"
								clearable
								clear-size="20"
								show-clear-icon
								:border="false"
							/>
						</view>
					</view>
				</view>

				<view class="profile-info-cell">
					<view class="profile-info-cell-left">
						<text class="profile-info-cell-label">手机号</text>
						<text class="profile-info-cell-value">{{ userInfo.mobile }}</text>
					</view>
					<view class="profile-info-cell-right" style="color: #ccc">不可修改</view>
				</view>
				<view class="profile-info-cell">
					<view class="profile-info-cell-left">
						<text class="profile-info-cell-label">实名信息</text>
						<!-- 身份证中间8位用*表示 -->
						<text class="profile-info-cell-value" v-if="userInfo.idCardNo">
							{{ userInfo.trueName || '-' }}({{
								(userInfo.idCardNo || '').replace(
									/(\d{4})\d{8}(\d{4})/,
									'$1********$2',
								)
							}})
						</text>
						<text class="profile-info-cell-value" v-else style="color: grey">
							未实名
						</text>
					</view>
					<view
						v-if="!userInfo.idCardNo"
						class="profile-info-cell-right"
						style="color: red"
						@click="onTrueNameAuthHandler"
					>
						未实名，立即认证
					</view>
					<view v-else class="profile-info-cell-right" style="color: green">已实名</view>
				</view>
				<view class="profile-info-cell">
					<view class="profile-info-cell-left">
						<text class="profile-info-cell-label">邮箱</text>
						<view class="profile-info-cell-value">
							<NutInput
								v-model="emailStr"
								placeholder="请输入邮箱"
								clearable
								clear-size="20"
								show-clear-icon
								:border="false"
							/>
						</view>
					</view>
				</view>
			</view>
			<!-- 保存修改 -->
			<nut-button
				type="info"
				style="margin: 30px 20px; width: calc(100% - 40px); border-radius: 5px"
				@click="onSubmitHandler"
			>
				保存修改
			</nut-button>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import './index.scss';
import { useUserStore } from '@/stores';
import {
	jwCloudAuth_SecurityCenter_System_User_AppUserEdit_POST,
	jwCloudPedestal_V1_Fastdfs_UploadFile_POST,
} from '@/utils/api';
import { getFullAvatarUrl, saveStore } from '@/utils/utils';
import DefaultAvatar from '@/assets/images/mine_avatar.png';
import Taro from '@tarojs/taro';

const userStore = useUserStore();

const userInfo = computed(() => userStore.getUserInfo());

// 上传头像的path
const avatarPathStr = ref('');

const avatarUrl = ref(getFullAvatarUrl(userInfo.value?.profilePicture) || DefaultAvatar);
// 图片加载失败时的回调
const onAvatarError = (_e) => {
	console.log('完整的图片url路径：', getFullAvatarUrl(userInfo.value?.profilePicture));
	avatarUrl.value = DefaultAvatar;
};

// 头像选择 - 使用文件上传方式
const onChooseAvatar = async (e) => {
	try {
		Taro.showLoading({ title: '上传头像中...' });
		// 等待1秒钟
		await new Promise((resolve) => setTimeout(resolve, 1000));
		// 通过微信头像 URL 下载并上传文件
		const uploadResult = await jwCloudPedestal_V1_Fastdfs_UploadFile_POST(e.detail.avatarUrl);
		avatarPathStr.value = uploadResult as string;
		// 更新本地头像显示
		avatarUrl.value = e.detail.avatarUrl;
		console.log('头像上传成功:', uploadResult, avatarUrl.value);

		Taro.showToast({
			title: '头像上传成功',
			icon: 'success',
		});
	} catch (error) {
		console.error('头像上传失败:', error);
		Taro.showToast({
			title: '头像上传失败',
			icon: 'error',
		});
	} finally {
		Taro.hideLoading();
	}
};

// 昵称
const nicknameStr = ref(userInfo.value?.nickname || '');

// 邮箱
const emailStr = ref(userInfo.value?.email || '');

// 实名认证
const onTrueNameAuthHandler = () => {
	Taro.navigateTo({
		url: '/pagesA/identityAuth/index',
	});
};

// 保存修改
const onSubmitHandler = async () => {
	try {
		Taro.showLoading({ title: '保存中...' });

		// 构建请求体，只有不为空的字段才会被包含
		const body = {
			// 必需字段
			id: userInfo.value?.id,

			// 条件字段：只有不为空时才添加
			...(nicknameStr.value !== '' && { nickname: nicknameStr.value }),
			...(emailStr.value !== '' && { email: emailStr.value }),
			...(avatarPathStr.value !== '' && { profilePicture: avatarPathStr.value }),
		};

		console.log('提交的数据:', body);

		// 调用更新用户信息接口
		await jwCloudAuth_SecurityCenter_System_User_AppUserEdit_POST(body);

		// 更新成功后，重新获取用户信息
		userInfo.value.email = emailStr.value;
		userInfo.value.nickname = nicknameStr.value;
		if (avatarPathStr.value !== '') {
			userInfo.value.profilePicture = avatarPathStr.value;
		}
		saveStore('userInfo', userInfo.value);
		userStore.setUserInfo(userInfo.value);

		Taro.showToast({
			title: '保存成功',
			icon: 'success',
		});

		Taro.navigateBack({
			delta: 1,
		});
	} catch (error) {
		console.error('保存失败:', error);
		Taro.showToast({
			title: '保存失败',
			icon: 'error',
		});
	} finally {
		Taro.hideLoading();
	}
};
</script>
