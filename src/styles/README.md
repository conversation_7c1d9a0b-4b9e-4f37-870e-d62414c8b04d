# 样式系统使用指南

本项目的样式系统提供了一套完整的设计令牌（Design Tokens），包括颜色、字体、间距、阴影等，帮助保持设计的一致性。

## 文件结构

```
src/styles/
├── index.ts      # 主样式文件，包含所有设计令牌
├── types.ts      # TypeScript 类型定义
├── styles_utils.ts      # 样式工具函数和常用组合
└── README.md     # 使用指南
```

## 基础使用

### 1. 导入样式

```typescript
// 导入完整主题
import theme from '@/styles';

// 或者按需导入
import { colors, fonts, spacing } from '@/styles';
```

### 2. 在 Vue 组件中使用

```vue
<template>
  <view class="container">
    <view class="card">
      <text class="title">标题</text>
      <text class="content">内容文本</text>
      <button class="btn-primary">主要按钮</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { colors, spacing, borderRadius, shadows } from '@/styles';
</script>

<style lang="scss">
.container {
  padding: v-bind('spacing[16]');
  background-color: v-bind('colors.background.primary');
}

.card {
  background-color: v-bind('colors.white');
  border-radius: v-bind('borderRadius.lg');
  box-shadow: v-bind('shadows.base');
  padding: v-bind('spacing[20]');
}

.title {
  font-size: v-bind('fonts.size["2xl"]');
  font-weight: v-bind('fonts.weight.bold');
  color: v-bind('colors.text.primary');
  margin-bottom: v-bind('spacing[8]');
}

.content {
  font-size: v-bind('fonts.size.base');
  color: v-bind('colors.text.secondary');
  line-height: v-bind('fonts.lineHeight.relaxed');
}

.btn-primary {
  background-color: v-bind('colors.primary[500]');
  color: v-bind('colors.white');
  padding: v-bind('spacing[12]') v-bind('spacing[24]');
  border-radius: v-bind('borderRadius.md');
  border: none;
  
  &:hover {
    background-color: v-bind('colors.primary[600]');
  }
}
</style>
```

### 3. 使用样式工具函数

```typescript
import { buttonVariants, cardVariants, textStyles } from '@/styles/utils';

// 在组件中使用预定义的样式变体
const primaryButtonStyle = buttonVariants.primary;
const defaultCardStyle = cardVariants.default;
const headingStyle = textStyles.heading.h2;
```

### 4. 在 CSS-in-JS 中使用

```typescript
import { colors, spacing } from '@/styles';

const styles = {
  container: {
    backgroundColor: colors.background.primary,
    padding: spacing[16],
  },
  button: {
    backgroundColor: colors.primary[500],
    color: colors.white,
    padding: `${spacing[12]} ${spacing[24]}`,
    borderRadius: borderRadius.md,
  },
};
```

## 颜色系统

### 主色调

- `colors.primary[500]` - 主品牌色
- `colors.primary[50-900]` - 主色调色板

### 语义化颜色

- `colors.success[500]` - 成功色
- `colors.warning[500]` - 警告色
- `colors.error[500]` - 错误色
- `colors.info[500]` - 信息色

### 文本颜色

- `colors.text.primary` - 主要文本
- `colors.text.secondary` - 次要文本
- `colors.text.disabled` - 禁用文本

### 背景颜色

- `colors.background.primary` - 主背景
- `colors.background.secondary` - 次背景
- `colors.background.tertiary` - 三级背景

## 字体系统

### 字体大小

```typescript
fonts.size.xs    // 20rpx (10px)
fonts.size.sm    // 24rpx (12px)
fonts.size.base  // 28rpx (14px)
fonts.size.lg    // 32rpx (16px)
fonts.size.xl    // 36rpx (18px)
fonts.size['2xl'] // 48rpx (24px)
```

### 字体粗细

```typescript
fonts.weight.normal    // 400
fonts.weight.medium    // 500
fonts.weight.semibold  // 600
fonts.weight.bold      // 700
```

## 间距系统

基于 8rpx 的间距系统：

```typescript
spacing[4]   // 8rpx (4px)
spacing[8]   // 16rpx (8px)
spacing[12]  // 24rpx (12px)
spacing[16]  // 32rpx (16px)
spacing[20]  // 40rpx (20px)
spacing[24]  // 48rpx (24px)
```

## 响应式设计

### 断点使用（仅 H5）

```scss
@media (min-width: v-bind('breakpoints.md')) {
  .container {
    padding: v-bind('spacing[32]');
  }
}
```

## 动画系统

### 动画时长

```typescript
duration[150]  // 150ms - 快速交互
duration[300]  // 300ms - 标准交互
duration[500]  // 500ms - 慢速交互
```

### 缓动函数

```typescript
easing.linear  // 线性
easing.in      // 缓入
easing.out     // 缓出
easing.inOut   // 缓入缓出
```

## 最佳实践

1. **保持一致性**：始终使用设计令牌而不是硬编码的值
2. **语义化命名**：使用语义化的颜色名称（如 `success`、`error`）而不是具体的颜色值
3. **响应式设计**：在 H5 端使用断点系统确保良好的响应式体验
4. **性能优化**：按需导入样式，避免导入整个样式库
5. **类型安全**：利用 TypeScript 类型定义确保样式的正确性

## 扩展样式系统

如需添加新的设计令牌，请在相应的文件中添加：

1. 在 `index.ts` 中添加新的样式值
2. 在 `types.ts` 中添加对应的类型定义
3. 在 `utils.ts` 中添加相关的工具函数（如需要）

这样可以确保整个样式系统的一致性和可维护性。
