/**
 * 样式工具函数和常用样式组合
 */

import { colors, fonts, spacing, borderRadius, shadows } from './index';
import type { ButtonVariant, CardStyle } from './types';

/**
 * 将 rpx 转换为对应的像素值（用于计算）
 * @param rpx rpx 值
 * @returns 对应的像素值
 */
export const rpxToPx = (rpx: string): number => {
	const value = parseFloat(rpx.replace('rpx', ''));
	return value / 2; // 750rpx = 375px
};

/**
 * 将像素值转换为 rpx
 * @param px 像素值
 * @returns rpx 字符串
 */
export const pxToRpx = (px: number): string => {
	return `${px * 2}rpx`;
};

/**
 * 创建响应式字体大小
 * @param baseSize 基础字体大小
 * @param scale 缩放比例
 * @returns 响应式字体大小对象
 */
export const createResponsiveFontSize = (baseSize: string, scale = 1.2) => {
	const basePx = rpxToPx(baseSize);
	return {
		sm: pxToRpx(basePx * 0.875),
		md: baseSize,
		lg: pxToRpx(basePx * scale),
		xl: pxToRpx(basePx * scale * scale),
	};
};

/**
 * 创建渐变背景
 * @param startColor 起始颜色
 * @param endColor 结束颜色
 * @param direction 渐变方向
 * @returns CSS 渐变字符串
 */
export const createGradient = (
	startColor: string,
	endColor: string,
	direction = 'to right',
): string => {
	return `linear-gradient(${direction}, ${startColor}, ${endColor})`;
};

/**
 * 创建按钮变体样式
 */
export const buttonVariants = {
	// 主要按钮
	primary: {
		backgroundColor: colors.primary[500],
		color: colors.white,
		borderColor: colors.primary[500],
		'&:hover': {
			backgroundColor: colors.primary[600],
			borderColor: colors.primary[600],
		},
		'&:active': {
			backgroundColor: colors.primary[700],
			borderColor: colors.primary[700],
		},
		'&:disabled': {
			backgroundColor: colors.gray[300],
			color: colors.gray[500],
			borderColor: colors.gray[300],
		},
	} as ButtonVariant,

	// 次要按钮
	secondary: {
		backgroundColor: colors.white,
		color: colors.primary[500],
		borderColor: colors.primary[500],
		'&:hover': {
			backgroundColor: colors.primary[50],
			borderColor: colors.primary[600],
		},
		'&:active': {
			backgroundColor: colors.primary[100],
			borderColor: colors.primary[700],
		},
		'&:disabled': {
			backgroundColor: colors.gray[100],
			color: colors.gray[400],
			borderColor: colors.gray[300],
		},
	} as ButtonVariant,

	// 成功按钮
	success: {
		backgroundColor: colors.success[500],
		color: colors.white,
		borderColor: colors.success[500],
		'&:hover': {
			backgroundColor: colors.success[600],
			borderColor: colors.success[600],
		},
		'&:active': {
			backgroundColor: colors.success[700],
			borderColor: colors.success[700],
		},
		'&:disabled': {
			backgroundColor: colors.gray[300],
			color: colors.gray[500],
			borderColor: colors.gray[300],
		},
	} as ButtonVariant,

	// 警告按钮
	warning: {
		backgroundColor: colors.warning[500],
		color: colors.white,
		borderColor: colors.warning[500],
		'&:hover': {
			backgroundColor: colors.warning[600],
			borderColor: colors.warning[600],
		},
		'&:active': {
			backgroundColor: colors.warning[700],
			borderColor: colors.warning[700],
		},
		'&:disabled': {
			backgroundColor: colors.gray[300],
			color: colors.gray[500],
			borderColor: colors.gray[300],
		},
	} as ButtonVariant,

	// 错误按钮
	error: {
		backgroundColor: colors.error[500],
		color: colors.white,
		borderColor: colors.error[500],
		'&:hover': {
			backgroundColor: colors.error[600],
			borderColor: colors.error[600],
		},
		'&:active': {
			backgroundColor: colors.error[700],
			borderColor: colors.error[700],
		},
		'&:disabled': {
			backgroundColor: colors.gray[300],
			color: colors.gray[500],
			borderColor: colors.gray[300],
		},
	} as ButtonVariant,

	// 幽灵按钮
	ghost: {
		backgroundColor: colors.transparent,
		color: colors.primary[500],
		borderColor: colors.transparent,
		'&:hover': {
			backgroundColor: colors.primary[50],
		},
		'&:active': {
			backgroundColor: colors.primary[100],
		},
		'&:disabled': {
			backgroundColor: colors.transparent,
			color: colors.gray[400],
		},
	} as ButtonVariant,
};

/**
 * 卡片样式变体
 */
export const cardVariants = {
	// 默认卡片
	default: {
		backgroundColor: colors.white,
		borderRadius: borderRadius.lg,
		boxShadow: shadows.base,
		padding: spacing[16],
		border: `1rpx solid ${colors.border.light}`,
	} as CardStyle,

	// 悬浮卡片
	elevated: {
		backgroundColor: colors.white,
		borderRadius: borderRadius.lg,
		boxShadow: shadows.lg,
		padding: spacing[20],
	} as CardStyle,

	// 简洁卡片
	minimal: {
		backgroundColor: colors.white,
		borderRadius: borderRadius.md,
		boxShadow: shadows.sm,
		padding: spacing[12],
		border: `1rpx solid ${colors.border.light}`,
	} as CardStyle,

	// 无边框卡片
	borderless: {
		backgroundColor: colors.background.secondary,
		borderRadius: borderRadius.lg,
		boxShadow: shadows.none,
		padding: spacing[16],
	} as CardStyle,
};

/**
 * 文本样式组合
 */
export const textStyles = {
	// 标题样式
	heading: {
		h1: {
			fontSize: fonts.size['5xl'],
			fontWeight: fonts.weight.bold,
			lineHeight: fonts.lineHeight.tight,
			color: colors.text.primary,
		},
		h2: {
			fontSize: fonts.size['4xl'],
			fontWeight: fonts.weight.bold,
			lineHeight: fonts.lineHeight.tight,
			color: colors.text.primary,
		},
		h3: {
			fontSize: fonts.size['3xl'],
			fontWeight: fonts.weight.semibold,
			lineHeight: fonts.lineHeight.snug,
			color: colors.text.primary,
		},
		h4: {
			fontSize: fonts.size['2xl'],
			fontWeight: fonts.weight.semibold,
			lineHeight: fonts.lineHeight.snug,
			color: colors.text.primary,
		},
		h5: {
			fontSize: fonts.size.xl,
			fontWeight: fonts.weight.medium,
			lineHeight: fonts.lineHeight.normal,
			color: colors.text.primary,
		},
		h6: {
			fontSize: fonts.size.lg,
			fontWeight: fonts.weight.medium,
			lineHeight: fonts.lineHeight.normal,
			color: colors.text.primary,
		},
	},

	// 正文样式
	body: {
		large: {
			fontSize: fonts.size.lg,
			fontWeight: fonts.weight.normal,
			lineHeight: fonts.lineHeight.relaxed,
			color: colors.text.primary,
		},
		default: {
			fontSize: fonts.size.base,
			fontWeight: fonts.weight.normal,
			lineHeight: fonts.lineHeight.normal,
			color: colors.text.primary,
		},
		small: {
			fontSize: fonts.size.sm,
			fontWeight: fonts.weight.normal,
			lineHeight: fonts.lineHeight.normal,
			color: colors.text.secondary,
		},
	},

	// 标签样式
	label: {
		large: {
			fontSize: fonts.size.base,
			fontWeight: fonts.weight.medium,
			lineHeight: fonts.lineHeight.normal,
			color: colors.text.primary,
		},
		default: {
			fontSize: fonts.size.sm,
			fontWeight: fonts.weight.medium,
			lineHeight: fonts.lineHeight.normal,
			color: colors.text.primary,
		},
		small: {
			fontSize: fonts.size.xs,
			fontWeight: fonts.weight.medium,
			lineHeight: fonts.lineHeight.normal,
			color: colors.text.secondary,
		},
	},
};

/**
 * 布局工具类
 */
export const layoutUtils = {
	// Flexbox 工具
	flex: {
		center: {
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
		},
		centerX: {
			display: 'flex',
			justifyContent: 'center',
		},
		centerY: {
			display: 'flex',
			alignItems: 'center',
		},
		between: {
			display: 'flex',
			justifyContent: 'space-between',
			alignItems: 'center',
		},
		around: {
			display: 'flex',
			justifyContent: 'space-around',
			alignItems: 'center',
		},
		column: {
			display: 'flex',
			flexDirection: 'column',
		},
		columnCenter: {
			display: 'flex',
			flexDirection: 'column',
			alignItems: 'center',
			justifyContent: 'center',
		},
	},

	// 定位工具
	position: {
		absolute: {
			position: 'absolute',
		},
		relative: {
			position: 'relative',
		},
		fixed: {
			position: 'fixed',
		},
		sticky: {
			position: 'sticky',
		},
	},

	// 尺寸工具
	size: {
		full: {
			width: '100%',
			height: '100%',
		},
		fullWidth: {
			width: '100%',
		},
		fullHeight: {
			height: '100%',
		},
		square: (size: string) => ({
			width: size,
			height: size,
		}),
	},
};

/**
 * 动画工具
 */
export const animations = {
	// 淡入淡出
	fadeIn: {
		opacity: 0,
		animation: 'fadeIn 0.3s ease-in-out forwards',
	},
	fadeOut: {
		opacity: 1,
		animation: 'fadeOut 0.3s ease-in-out forwards',
	},

	// 滑动
	slideInUp: {
		transform: 'translateY(100%)',
		animation: 'slideInUp 0.3s ease-out forwards',
	},
	slideInDown: {
		transform: 'translateY(-100%)',
		animation: 'slideInDown 0.3s ease-out forwards',
	},

	// 缩放
	scaleIn: {
		transform: 'scale(0)',
		animation: 'scaleIn 0.3s ease-out forwards',
	},
	scaleOut: {
		transform: 'scale(1)',
		animation: 'scaleOut 0.3s ease-in forwards',
	},
};
