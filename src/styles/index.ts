/**
 * 全局样式常量定义
 * 包含颜色、字体、间距、阴影等常用样式属性
 */

// 颜色系统
export const colors = {
	// 主色调
	primary: {
		50: '#f0f5ff', // 最浅
		100: '#d6e4ff',
		200: '#adc8ff',
		300: '#7da9ff',
		400: '#4d8dff',
		500: '#397fef', // 主色保持不变
		600: '#2c6cd6',
		700: '#1f58bd',
		800: '#1344a4',
		900: '#0a308b', // 最深
	},

	// 辅助色
	secondary: {
		50: '#fafafa',
		100: '#f4f4f5',
		200: '#e4e4e7',
		300: '#d4d4d8',
		400: '#a1a1aa',
		500: '#71717a',
		600: '#52525b',
		700: '#3f3f46',
		800: '#27272a',
		900: '#18181b',
	},

	// 成功色
	success: {
		50: '#f0fdf4',
		100: '#dcfce7',
		200: '#bbf7d0',
		300: '#86efac',
		400: '#4ade80',
		500: '#22c55e', // 主成功色
		600: '#16a34a',
		700: '#15803d',
		800: '#166534',
		900: '#14532d',
	},

	// 警告色
	warning: {
		50: '#fffbeb',
		100: '#fef3c7',
		200: '#fde68a',
		300: '#fcd34d',
		400: '#fbbf24',
		500: '#f59e0b', // 主警告色
		600: '#d97706',
		700: '#b45309',
		800: '#92400e',
		900: '#78350f',
	},

	// 错误色
	error: {
		50: '#fef2f2',
		100: '#fee2e2',
		200: '#fecaca',
		300: '#fca5a5',
		400: '#f87171',
		500: '#ef4444', // 主错误色
		600: '#dc2626',
		700: '#b91c1c',
		800: '#991b1b',
		900: '#7f1d1d',
	},

	// 信息色
	info: {
		50: '#eff6ff',
		100: '#dbeafe',
		200: '#bfdbfe',
		300: '#93c5fd',
		400: '#60a5fa',
		500: '#3b82f6', // 主信息色
		600: '#2563eb',
		700: '#1d4ed8',
		800: '#1e40af',
		900: '#1e3a8a',
	},

	// 中性色
	gray: {
		50: '#f9fafb',
		100: '#f3f4f6',
		200: '#e5e7eb',
		300: '#d1d5db',
		400: '#9ca3af',
		500: '#6b7280',
		600: '#4b5563',
		700: '#374151',
		800: '#1f2937',
		900: '#111827',
	},

	// 常用颜色快捷方式
	white: '#ffffff',
	black: '#000000',
	transparent: 'transparent',

	// 文本颜色
	text: {
		primary: '#1f2937', // 主要文本
		secondary: '#6b7280', // 次要文本
		disabled: '#9ca3af', // 禁用文本
		inverse: '#ffffff', // 反色文本
	},

	// 背景色
	background: {
		primary: '#ffffff', // 主背景
		secondary: '#f9fafb', // 次背景
		tertiary: '#f3f4f6', // 三级背景
		overlay: 'rgba(0, 0, 0, 0.5)', // 遮罩层
	},

	// 边框色
	border: {
		light: '#e5e7eb',
		default: '#d1d5db',
		dark: '#9ca3af',
	},
};

// 字体系统
export const fonts = {
	// 字体族
	family: {
		sans: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
		serif: 'Georgia, "Times New Roman", Times, serif',
		mono: 'Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
		chinese:
			'"PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif',
	},

	// 字体大小 (基于 Taro 的 rpx 单位)
	size: {
		xs: '20rpx', // 10px
		sm: '24rpx', // 12px
		base: '28rpx', // 14px
		lg: '32rpx', // 16px
		xl: '36rpx', // 18px
		'2xl': '48rpx', // 24px
		'3xl': '60rpx', // 30px
		'4xl': '72rpx', // 36px
		'5xl': '96rpx', // 48px
		'6xl': '120rpx', // 60px
	},

	// 字体粗细
	weight: {
		thin: '100',
		light: '300',
		normal: '400',
		medium: '500',
		semibold: '600',
		bold: '700',
		extrabold: '800',
		black: '900',
	},

	// 行高
	lineHeight: {
		none: '1',
		tight: '1.25',
		snug: '1.375',
		normal: '1.5',
		relaxed: '1.625',
		loose: '2',
	},
};

// 间距系统 (基于 Taro 的 rpx 单位)
export const spacing = {
	0: '0',
	1: '2rpx', // 1px
	2: '4rpx', // 2px
	3: '6rpx', // 3px
	4: '8rpx', // 4px
	5: '10rpx', // 5px
	6: '12rpx', // 6px
	8: '16rpx', // 8px
	10: '20rpx', // 10px
	12: '24rpx', // 12px
	16: '32rpx', // 16px
	20: '40rpx', // 20px
	24: '48rpx', // 24px
	32: '64rpx', // 32px
	40: '80rpx', // 40px
	48: '96rpx', // 48px
	56: '112rpx', // 56px
	64: '128rpx', // 64px
	80: '160rpx', // 80px
	96: '192rpx', // 96px
};

// 圆角系统
export const borderRadius = {
	none: '0',
	sm: '4rpx', // 2px
	base: '8rpx', // 4px
	md: '12rpx', // 6px
	lg: '16rpx', // 8px
	xl: '24rpx', // 12px
	'2xl': '32rpx', // 16px
	'3xl': '48rpx', // 24px
	full: '9999rpx',
};

// 阴影系统
export const shadows = {
	none: 'none',
	sm: '0 2rpx 4rpx rgba(0, 0, 0, 0.05)',
	base: '0 2rpx 8rpx rgba(0, 0, 0, 0.1)',
	md: '0 8rpx 16rpx rgba(0, 0, 0, 0.1)',
	lg: '0 16rpx 32rpx rgba(0, 0, 0, 0.15)',
	xl: '0 32rpx 64rpx rgba(0, 0, 0, 0.2)',
	inner: 'inset 0 4rpx 8rpx rgba(0, 0, 0, 0.1)',
};

// Z-index 层级
export const zIndex = {
	hide: -1,
	auto: 'auto',
	base: 0,
	docked: 10,
	dropdown: 1000,
	sticky: 1100,
	banner: 1200,
	overlay: 1300,
	modal: 1400,
	popover: 1500,
	skipLink: 1600,
	toast: 1700,
	tooltip: 1800,
};

// 动画时长
export const duration = {
	75: '75ms',
	100: '100ms',
	150: '150ms',
	200: '200ms',
	300: '300ms',
	500: '500ms',
	700: '700ms',
	1000: '1000ms',
};

// 动画缓动函数
export const easing = {
	linear: 'linear',
	in: 'cubic-bezier(0.4, 0, 1, 1)',
	out: 'cubic-bezier(0, 0, 0.2, 1)',
	inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
};

// 断点系统 (适用于 H5)
export const breakpoints = {
	sm: '640px',
	md: '768px',
	lg: '1024px',
	xl: '1280px',
	'2xl': '1536px',
};

// 导出默认主题
export const theme = {
	colors,
	fonts,
	spacing,
	borderRadius,
	shadows,
	zIndex,
	duration,
	easing,
	breakpoints,
};

export default theme;
