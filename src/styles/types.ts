/**
 * 样式系统类型定义
 */

// 颜色类型
export interface ColorScale {
	50: string;
	100: string;
	200: string;
	300: string;
	400: string;
	500: string;
	600: string;
	700: string;
	800: string;
	900: string;
}

export interface TextColors {
	primary: string;
	secondary: string;
	disabled: string;
	inverse: string;
}

export interface BackgroundColors {
	primary: string;
	secondary: string;
	tertiary: string;
	overlay: string;
}

export interface BorderColors {
	light: string;
	default: string;
	dark: string;
}

export interface Colors {
	primary: ColorScale;
	secondary: ColorScale;
	success: ColorScale;
	warning: ColorScale;
	error: ColorScale;
	info: ColorScale;
	gray: ColorScale;
	white: string;
	black: string;
	transparent: string;
	text: TextColors;
	background: BackgroundColors;
	border: BorderColors;
}

// 字体类型
export interface FontFamily {
	sans: string;
	serif: string;
	mono: string;
	chinese: string;
}

export interface FontSize {
	xs: string;
	sm: string;
	base: string;
	lg: string;
	xl: string;
	'2xl': string;
	'3xl': string;
	'4xl': string;
	'5xl': string;
	'6xl': string;
}

export interface FontWeight {
	thin: string;
	light: string;
	normal: string;
	medium: string;
	semibold: string;
	bold: string;
	extrabold: string;
	black: string;
}

export interface LineHeight {
	none: string;
	tight: string;
	snug: string;
	normal: string;
	relaxed: string;
	loose: string;
}

export interface Fonts {
	family: FontFamily;
	size: FontSize;
	weight: FontWeight;
	lineHeight: LineHeight;
}

// 间距类型
export interface Spacing {
	0: string;
	1: string;
	2: string;
	3: string;
	4: string;
	5: string;
	6: string;
	8: string;
	10: string;
	12: string;
	16: string;
	20: string;
	24: string;
	32: string;
	40: string;
	48: string;
	56: string;
	64: string;
	80: string;
	96: string;
}

// 圆角类型
export interface BorderRadius {
	none: string;
	sm: string;
	base: string;
	md: string;
	lg: string;
	xl: string;
	'2xl': string;
	'3xl': string;
	full: string;
}

// 阴影类型
export interface Shadows {
	none: string;
	sm: string;
	base: string;
	md: string;
	lg: string;
	xl: string;
	inner: string;
}

// Z-index 类型
export interface ZIndex {
	hide: number;
	auto: string;
	base: number;
	docked: number;
	dropdown: number;
	sticky: number;
	banner: number;
	overlay: number;
	modal: number;
	popover: number;
	skipLink: number;
	toast: number;
	tooltip: number;
}

// 动画类型
export interface Duration {
	75: string;
	100: string;
	150: string;
	200: string;
	300: string;
	500: string;
	700: string;
	1000: string;
}

export interface Easing {
	linear: string;
	in: string;
	out: string;
	inOut: string;
}

// 断点类型
export interface Breakpoints {
	sm: string;
	md: string;
	lg: string;
	xl: string;
	'2xl': string;
}

// 主题类型
export interface Theme {
	colors: Colors;
	fonts: Fonts;
	spacing: Spacing;
	borderRadius: BorderRadius;
	shadows: Shadows;
	zIndex: ZIndex;
	duration: Duration;
	easing: Easing;
	breakpoints: Breakpoints;
}

// 常用样式组合类型
export interface ButtonVariant {
	backgroundColor: string;
	color: string;
	borderColor?: string;
	'&:hover'?: {
		backgroundColor: string;
		borderColor?: string;
	};
	'&:active'?: {
		backgroundColor: string;
		borderColor?: string;
	};
	'&:disabled'?: {
		backgroundColor: string;
		color: string;
		borderColor?: string;
	};
}

export interface CardStyle {
	backgroundColor: string;
	borderRadius: string;
	boxShadow: string;
	padding: string;
	border?: string;
}
