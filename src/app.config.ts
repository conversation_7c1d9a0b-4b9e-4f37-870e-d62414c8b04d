export default {
	// 主包页面路径列表（包含 tabBar 页面）
	pages: ['pages/login/index', 'pages/index/index', 'pages/message/index', 'pages/mine/index'],
	// 分包结构配置（用于其他功能页面）
	subPackages: [
		// 可以在这里添加其他功能模块的分包
		// 例如：用户设置、商品详情等非 tabBar 页面
		{
			root: 'pagesA',
			pages: ['enterpriseAuth/index', 'identityAuth/index', 'profile/index'],
		},
	],

	// 底部 tab 栏的表现
	tabBar: {
		color: '#d4d4d8',
		selectedColor: '#397fef',
		backgroundColor: '#FFFFFF',
		list: [
			{
				pagePath: 'pages/index/index',
				text: '首页',
				iconPath: './assets/tab/home.png',
				selectedIconPath: './assets/tab/home_active.png',
			},
			{
				pagePath: 'pages/message/index',
				text: '消息',
				iconPath: './assets/tab/message.png',
				selectedIconPath: './assets/tab/message_active.png',
			},
			{
				pagePath: 'pages/mine/index',
				text: '个人中心',
				iconPath: './assets/tab/mine.png',
				selectedIconPath: './assets/tab/mine_active.png',
			},
		],
	},
	window: {
		backgroundTextStyle: 'light',
		navigationBarBackgroundColor: '#fff',
		navigationBarTitleText: 'WeChat',
		navigationBarTextStyle: 'black',
	},
	networkTimeout: {
		request: 60000,
		connectSocket: 60000,
		uploadFile: 60000,
		downloadFile: 60000,
	},
	__usePrivacyCheck__: true,
	useExtendedLib: {
		weui: true,
	},
};
