/**
 * 小程序页面生命周期 Hooks
 * 提供统一的页面生命周期管理，支持多端兼容
 */

import { onMounted, onUnmounted, onActivated, onDeactivated, ref } from 'vue';
import {
	useDidShow,
	useDidHide,
	useLoad,
	useUnload,
	usePullDownRefresh,
	useReachBottom,
} from '@tarojs/taro';

// 页面状态类型
export interface PageState {
	isLoading: boolean;
	isVisible: boolean;
	isFirstLoad: boolean;
	loadError: Error | null;
}

// 生命周期回调类型
export interface LifecycleCallbacks {
	onLoad?: (options?: any) => void | Promise<void>;
	onShow?: () => void | Promise<void>;
	onHide?: () => void | Promise<void>;
	onUnload?: () => void | Promise<void>;
	onPullDownRefresh?: () => void | Promise<void>;
	onReachBottom?: () => void | Promise<void>;
	onError?: (error: Error) => void;
}

// 页面配置选项
export interface PageOptions {
	// 是否启用下拉刷新
	enablePullDownRefresh?: boolean;
	// 是否启用上拉加载
	enableReachBottom?: boolean;
	// 距离底部多少像素触发上拉加载
	onReachBottomDistance?: number;
	// 是否自动处理加载状态
	autoLoading?: boolean;
	// 是否启用页面埋点
	enableAnalytics?: boolean;
}

/**
 * 页面生命周期 Hook
 * @param callbacks 生命周期回调函数
 * @param options 页面配置选项
 * @returns 页面状态和工具方法
 */
export function useLifecycle(callbacks: LifecycleCallbacks = {}, options: PageOptions = {}) {
	const { onLoad, onShow, onHide, onUnload, onPullDownRefresh, onReachBottom, onError } =
		callbacks;

	const {
		enablePullDownRefresh = false,
		enableReachBottom = false,
		autoLoading = true,
		enableAnalytics = false,
	} = options;

	// 页面状态
	const pageState = ref<PageState>({
		isLoading: false,
		isVisible: false,
		isFirstLoad: true,
		loadError: null,
	});

	// 错误处理
	const handleError = (error: Error, context: string) => {
		console.error(`[${context}] 页面错误:`, error);
		pageState.value.loadError = error;
		pageState.value.isLoading = false;
		onError?.(error);
	};

	// 安全执行异步回调
	const safeExecute = async (
		callback: (() => void | Promise<void>) | undefined,
		context: string,
	) => {
		if (!callback) return;

		try {
			if (autoLoading && context !== 'onHide' && context !== 'onUnload') {
				pageState.value.isLoading = true;
			}

			await callback();

			if (autoLoading) {
				pageState.value.isLoading = false;
				pageState.value.loadError = null;
			}
		} catch (error) {
			handleError(error as Error, context);
		}
	};

	// 页面加载
	useLoad(async (options) => {
		console.log('[Lifecycle] 页面加载', options);

		if (enableAnalytics) {
			// 这里可以添加页面访问埋点
			console.log('[Analytics] 页面访问埋点');
		}

		await safeExecute(onLoad ? () => onLoad(options) : undefined, 'onLoad');
	});

	// 页面显示
	useDidShow(async () => {
		console.log('[Lifecycle] 页面显示');
		pageState.value.isVisible = true;

		await safeExecute(onShow, 'onShow');

		if (pageState.value.isFirstLoad) {
			pageState.value.isFirstLoad = false;
		}
	});

	// 页面隐藏
	useDidHide(async () => {
		console.log('[Lifecycle] 页面隐藏');
		pageState.value.isVisible = false;

		await safeExecute(onHide, 'onHide');
	});

	// 页面卸载
	useUnload(async () => {
		console.log('[Lifecycle] 页面卸载');

		if (enableAnalytics) {
			// 这里可以添加页面离开埋点
			console.log('[Analytics] 页面离开埋点');
		}

		await safeExecute(onUnload, 'onUnload');
	});

	// 下拉刷新
	if (enablePullDownRefresh) {
		usePullDownRefresh(async () => {
			console.log('[Lifecycle] 下拉刷新');
			await safeExecute(onPullDownRefresh, 'onPullDownRefresh');
		});
	}

	// 上拉加载
	if (enableReachBottom) {
		useReachBottom(async () => {
			console.log('[Lifecycle] 上拉加载');
			await safeExecute(onReachBottom, 'onReachBottom');
		});
	}

	// H5 兼容性处理
	onMounted(() => {
		console.log('[Lifecycle] H5 页面挂载');
	});

	onUnmounted(() => {
		console.log('[Lifecycle] H5 页面卸载');
	});

	onActivated(() => {
		console.log('[Lifecycle] H5 页面激活');
	});

	onDeactivated(() => {
		console.log('[Lifecycle] H5 页面失活');
	});

	// 工具方法
	const setLoading = (loading: boolean) => {
		pageState.value.isLoading = loading;
	};

	const clearError = () => {
		pageState.value.loadError = null;
	};

	const refresh = async () => {
		if (onShow) {
			await safeExecute(onShow, 'refresh');
		}
	};

	return {
		// 页面状态
		pageState: pageState.value,
		isLoading: pageState.value.isLoading,
		isVisible: pageState.value.isVisible,
		isFirstLoad: pageState.value.isFirstLoad,
		loadError: pageState.value.loadError,

		// 工具方法
		setLoading,
		clearError,
		refresh,
		handleError,
	};
}

/**
 * 简化版页面生命周期 Hook
 * 适用于简单页面
 */
export function useSimpleLifecycle(
	onPageLoad?: () => void | Promise<void>,
	onPageShow?: () => void | Promise<void>,
) {
	return useLifecycle({
		onLoad: onPageLoad,
		onShow: onPageShow,
	});
}

/**
 * 列表页面生命周期 Hook
 * 包含下拉刷新和上拉加载
 */
export function useListLifecycle(
	onLoadData?: () => void | Promise<void>,
	onRefresh?: () => void | Promise<void>,
	onLoadMore?: () => void | Promise<void>,
) {
	return useLifecycle(
		{
			onLoad: onLoadData,
			onShow: onLoadData,
			onPullDownRefresh: onRefresh,
			onReachBottom: onLoadMore,
		},
		{
			enablePullDownRefresh: true,
			enableReachBottom: true,
			autoLoading: true,
		},
	);
}
