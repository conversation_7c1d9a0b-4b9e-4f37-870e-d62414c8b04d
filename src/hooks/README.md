# Hooks 使用指南

本目录包含项目中的自定义 Hooks，提供可复用的逻辑组合。

## useLifecycle - 页面生命周期 Hook

### 功能特性

- 🔄 统一的页面生命周期管理
- 📱 多端兼容（H5、小程序、React Native）
- 🔒 TypeScript 类型安全
- 🚀 自动加载状态管理
- 📊 内置页面埋点支持
- 🔧 灵活的配置选项

### 基础使用

```vue
<template>
  <view class="page">
    <view v-if="isLoading">加载中...</view>
    <view v-else>页面内容</view>
  </view>
</template>

<script setup>
import { useLifecycle } from '@/hooks/useLifecycle';

const { pageState, isLoading, setLoading } = useLifecycle({
  onLoad: async (options) => {
    console.log('页面加载，参数:', options);
    // 初始化数据
    await loadInitialData();
  },
  onShow: async () => {
    console.log('页面显示');
    // 刷新数据
    await refreshData();
  },
  onHide: () => {
    console.log('页面隐藏');
    // 清理定时器等
  },
  onUnload: () => {
    console.log('页面卸载');
    // 清理资源
  }
}, {
  autoLoading: true,
  enableAnalytics: true
});

const loadInitialData = async () => {
  // 数据加载逻辑
};

const refreshData = async () => {
  // 数据刷新逻辑
};
</script>
```

### 列表页面使用

```vue
<script setup>
import { useListLifecycle } from '@/hooks/useLifecycle';

const { pageState, isLoading } = useListLifecycle(
  // 初始加载数据
  async () => {
    await loadListData();
  },
  // 下拉刷新
  async () => {
    await refreshListData();
  },
  // 上拉加载更多
  async () => {
    await loadMoreData();
  }
);

const loadListData = async () => {
  // 加载列表数据
};

const refreshListData = async () => {
  // 刷新列表数据
};

const loadMoreData = async () => {
  // 加载更多数据
};
</script>
```

### 简化使用

```vue
<script setup>
import { useSimpleLifecycle } from '@/hooks/useLifecycle';

const { isLoading } = useSimpleLifecycle(
  // 页面加载
  async () => {
    await initPage();
  },
  // 页面显示
  async () => {
    await refreshPage();
  }
);
</script>
```

### API 参考

#### useLifecycle(callbacks, options)

**参数：**

- `callbacks: LifecycleCallbacks` - 生命周期回调函数
- `options: PageOptions` - 页面配置选项

**返回值：**

```typescript
{
  // 页面状态
  pageState: PageState;
  isLoading: boolean;
  isVisible: boolean;
  isFirstLoad: boolean;
  loadError: Error | null;
  
  // 工具方法
  setLoading: (loading: boolean) => void;
  clearError: () => void;
  refresh: () => Promise<void>;
  handleError: (error: Error, context: string) => void;
}
```

#### LifecycleCallbacks

```typescript
interface LifecycleCallbacks {
  onLoad?: (options?: any) => void | Promise<void>;
  onShow?: () => void | Promise<void>;
  onHide?: () => void | Promise<void>;
  onUnload?: () => void | Promise<void>;
  onPullDownRefresh?: () => void | Promise<void>;
  onReachBottom?: () => void | Promise<void>;
  onError?: (error: Error) => void;
}
```

#### PageOptions

```typescript
interface PageOptions {
  enablePullDownRefresh?: boolean;  // 启用下拉刷新
  enableReachBottom?: boolean;      // 启用上拉加载
  onReachBottomDistance?: number;   // 触发距离
  autoLoading?: boolean;            // 自动加载状态
  enableAnalytics?: boolean;        // 启用埋点
}
```

### 最佳实践

1. **错误处理**：所有异步操作都会自动捕获错误
2. **加载状态**：启用 `autoLoading` 自动管理加载状态
3. **性能优化**：避免在 `onShow` 中执行重复的数据加载
4. **资源清理**：在 `onHide` 和 `onUnload` 中清理定时器和监听器

### 扩展示例

#### 带错误处理的页面

```vue
<script setup>
import { useLifecycle } from '@/hooks/useLifecycle';

const { pageState, isLoading, loadError, clearError } = useLifecycle({
  onLoad: async () => {
    // 可能失败的数据加载
    await riskyDataLoad();
  },
  onError: (error) => {
    // 自定义错误处理
    console.error('页面错误:', error);
    // 可以显示错误提示
  }
});

const retryLoad = async () => {
  clearError();
  await riskyDataLoad();
};
</script>

<template>
  <view class="page">
    <view v-if="isLoading">加载中...</view>
    <view v-else-if="loadError">
      <text>加载失败: {{ loadError.message }}</text>
      <button @click="retryLoad">重试</button>
    </view>
    <view v-else>正常内容</view>
  </view>
</template>
```

这个 Hook 为你的项目提供了统一、可靠的页面生命周期管理，特别适合 Taro 多端开发的需求。
