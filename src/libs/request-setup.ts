/**
 * 请求库初始化配置
 * 在应用启动时调用
 */

import request from './request';
import { getApiConfig } from '@/config/api';

/**
 * 初始化请求库
 */
export const setupRequest = (): void => {
	const config = getApiConfig();

	// 设置基础配置
	request.setBaseURL(config.baseURL);
	request.setDefaultConfig({
		timeout: config.timeout,
		showLoading: false,
		showError: true,
		needAuth: true,
		retry: 1,
	});

	// 添加自定义拦截器
	setupCustomInterceptors();

	console.log('🌐 请求库初始化完成', {
		baseURL: config.baseURL,
		timeout: config.timeout,
		enableLog: config.enableLog,
	});
};

/**
 * 设置自定义拦截器
 */
const setupCustomInterceptors = (): void => {
	// 请求拦截器 - 添加时间戳防缓存
	request.addRequestInterceptor((config) => {
		if (config.method === 'GET') {
			const timestamp = Date.now();
			const separator = config.url.includes('?') ? '&' : '?';
			config.url = `${config.url}${separator}_t=${timestamp}`;
		}
		return config;
	});

	// 请求拦截器 - 日志记录
	request.addRequestInterceptor((config) => {
		const apiConfig = getApiConfig();
		if (apiConfig.enableLog) {
			console.log('📤 请求发送:', {
				url: config.url,
				method: config.method,
				data: config.data,
				header: config.header,
			});
		}
		return config;
	});

	// 响应拦截器 - 日志记录
	request.addResponseInterceptor((response) => {
		const apiConfig = getApiConfig();
		if (apiConfig.enableLog) {
			console.log('📥 响应接收:', {
				url: response.config?.url,
				statusCode: response.statusCode,
				data: response.data,
			});
		}
		return response;
	});

	// 错误拦截器 - 网络错误处理
	request.addErrorInterceptor((error) => {
		if (error.errMsg && error.errMsg.includes('network')) {
			throw new Error('网络连接失败，请检查网络设置');
		}

		if (error.errMsg && error.errMsg.includes('timeout')) {
			throw new Error('请求超时，请稍后重试');
		}

		throw error;
	});

	// 错误拦截器 - 业务错误处理
	request.addErrorInterceptor((error) => {
		const apiConfig = getApiConfig();
		if (apiConfig.enableLog) {
			console.error('❌ 请求错误:', {
				message: error.message,
				statusCode: error.statusCode,
				errMsg: error.errMsg,
			});
		}
		throw error;
	});
};
