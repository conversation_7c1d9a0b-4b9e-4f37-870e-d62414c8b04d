import { getStore } from '@/utils/utils';
import { defineStore } from 'pinia';

// 定义 auth 模块
export const useAuthStore = defineStore('auth', {
	state: () => ({
		token: '',
	}),
	actions: {
		setToken(token: string) {
			this.token = token;
		},
		clearToken() {
			this.token = '';
		},
		getToken() {
			return this.token;
		},
		isLogin() {
			return this.token !== '';
		},
	},
});

export const useUserStore = defineStore('user', {
	state: () => ({
		userInfo: null,
	}),
	actions: {
		setUserInfo(userInfo: any) {
			this.userInfo = userInfo;
		},
		clearUserInfo() {
			this.userInfo = null;
		},
		getUserInfo() {
			return this.userInfo;
		},
		isLogin() {
			return this.userInfo !== null;
		},
	},
});

export const initStore = () => {
	const authStore = useAuthStore();
	const userStore = useUserStore();
	const token = getStore('token');
	const userInfo = getStore('userInfo');
	console.log('初始化登录信息：token:', token, 'userInfo:', userInfo);
	if (token) {
		authStore.setToken(token);
	}
	if (userInfo) {
		userStore.setUserInfo(userInfo);
	}
};

export const demoTestCounterStore = defineStore('counter', {
	state: () => {
		return { count: 0 };
	},
	actions: {
		increment() {
			this.count++;
		},
	},
});
