/**
 * 认证相关 API
 */

import request from '@/libs/request';
import { API_PATHS } from '@/config/api';
import Taro from '@tarojs/taro';

// 登录参数类型
export interface LoginParams {
	username: string;
	password: string;
	captcha?: string;
}

// 微信登录参数类型
export interface WechatLoginParams {
	code: string;
	userInfo: {
		nickName: string;
		avatarUrl: string;
		gender: number;
		city: string;
		province: string;
		country: string;
	};
	platform: string;
}

// 手机号登录参数类型
export interface PhoneLoginParams {
	phone: string;
	code: string;
}

// 登录响应类型
export interface LoginResponse {
	token: string;
	refreshToken?: string;
	userInfo: {
		id: string;
		username: string;
		nickname: string;
		avatar: string;
		phone?: string;
		email?: string;
		roles: string[];
		permissions: string[];
	};
	expiresIn: number;
}

// 用户信息类型
export interface UserProfile {
	id: string;
	username: string;
	nickname: string;
	avatar: string;
	phone?: string;
	email?: string;
	gender?: number;
	birthday?: string;
	city?: string;
	province?: string;
	signature?: string;
	roles: string[];
	permissions: string[];
	createdAt: string;
	updatedAt: string;
}

/**
 * 用户名密码登录
 */
export const login = (params: LoginParams): Promise<LoginResponse> => {
	return request.post<LoginResponse>(API_PATHS.AUTH.LOGIN, params, {
		showLoading: true,
		loadingText: '登录中...',
		needAuth: false,
	});
};

/**
 * 微信登录
 */
export const wechatLogin = (params: WechatLoginParams): Promise<LoginResponse> => {
	return request.post<LoginResponse>(API_PATHS.AUTH.WECHAT_LOGIN, params, {
		showLoading: true,
		loadingText: '微信登录中...',
		needAuth: false,
	});
};

/**
 * 手机号登录
 */
export const phoneLogin = (params: PhoneLoginParams): Promise<LoginResponse> => {
	return request.post<LoginResponse>(API_PATHS.AUTH.PHONE_LOGIN, params, {
		showLoading: true,
		loadingText: '登录中...',
		needAuth: false,
	});
};

/**
 * 退出登录
 */
export const logout = (): Promise<void> => {
	return request.post<void>(
		API_PATHS.AUTH.LOGOUT,
		{},
		{
			showLoading: false,
			showError: false,
		},
	);
};

/**
 * 刷新 Token
 */
export const refreshToken = (
	refreshToken: string,
): Promise<{ token: string; expiresIn: number }> => {
	return request.post(
		API_PATHS.AUTH.REFRESH_TOKEN,
		{ refreshToken },
		{
			needAuth: false,
			showLoading: false,
			showError: false,
		},
	);
};

/**
 * 获取用户信息
 */
export const getUserProfile = (): Promise<UserProfile> => {
	return request.get<UserProfile>(API_PATHS.USER.PROFILE);
};

/**
 * 更新用户信息
 */
export const updateUserProfile = (data: Partial<UserProfile>): Promise<UserProfile> => {
	return request.put<UserProfile>(API_PATHS.USER.UPDATE_PROFILE, data, {
		showLoading: true,
		loadingText: '保存中...',
	});
};

/**
 * 修改密码
 */
export const changePassword = (data: {
	oldPassword: string;
	newPassword: string;
}): Promise<void> => {
	return request.post<void>(API_PATHS.USER.CHANGE_PASSWORD, data, {
		showLoading: true,
		loadingText: '修改中...',
	});
};

/**
 * 上传头像
 */
export const uploadAvatar = (filePath: string): Promise<{ url: string }> => {
	return new Promise((resolve, reject) => {
		import('@/stores').then(({ useAuthStore }) => {
			const authStore = useAuthStore();

			// 使用 Taro.uploadFile 上传文件
			Taro.uploadFile({
				url: `${request.baseURL || ''}${API_PATHS.USER.AVATAR_UPLOAD}`,
				filePath,
				name: 'avatar',
				header: {
					Authorization: `Bearer ${authStore.token}`,
				},
				success: (res) => {
					try {
						const data = JSON.parse(res.data);
						if (data.code === 0) {
							resolve(data.data);
						} else {
							reject(new Error(data.message || '上传失败'));
						}
					} catch (error) {
						reject(new Error('上传失败'));
					}
				},
				fail: (error) => {
					reject(error);
				},
			});
		});
	});
};
