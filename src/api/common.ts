/**
 * 通用 API 服务
 */

import request from '@/libs/request';
import { API_PATHS, getApiConfig } from '@/config/api';
import Taro from '@tarojs/taro';

// 分页参数类型
export interface PaginationParams {
	page: number;
	pageSize: number;
	keyword?: string;
	sortBy?: string;
	sortOrder?: 'asc' | 'desc';
}

// 分页响应类型
export interface PaginationResponse<T> {
	list: T[];
	total: number;
	page: number;
	pageSize: number;
	totalPages: number;
}

// 文件上传响应类型
export interface UploadResponse {
	url: string;
	filename: string;
	size: number;
	type: string;
}

// 系统配置类型
export interface SystemConfig {
	appName: string;
	version: string;
	apiVersion: string;
	features: string[];
	maintenance: boolean;
	maintenanceMessage?: string;
}

/**
 * 获取系统配置
 */
export const getSystemConfig = (): Promise<SystemConfig> => {
	return request.get<SystemConfig>(
		API_PATHS.SYSTEM.CONFIG,
		{},
		{
			needAuth: false,
			showLoading: false,
			showError: false,
		},
	);
};

/**
 * 获取版本信息
 */
export const getVersionInfo = (): Promise<{ version: string; updateUrl?: string }> => {
	return request.get(
		API_PATHS.SYSTEM.VERSION,
		{},
		{
			needAuth: false,
			showLoading: false,
			showError: false,
		},
	);
};

/**
 * 通用文件上传
 */
export const uploadFile = (
	filePath: string,
	type: 'image' | 'file' = 'image',
): Promise<UploadResponse> => {
	return new Promise((resolve, reject) => {
		const uploadPath = type === 'image' ? API_PATHS.UPLOAD.IMAGE : API_PATHS.UPLOAD.FILE;

		Taro.showLoading({ title: '上传中...' });

		Taro.uploadFile({
			url: `${getApiConfig().baseURL}${uploadPath}`,
			filePath,
			name: 'file',
			header: {
				Authorization: `Bearer token_placeholder`,
			},
			success: (res) => {
				try {
					const data = JSON.parse(res.data);
					if (data.code === 0) {
						resolve(data.data);
					} else {
						reject(new Error(data.message || '上传失败'));
					}
				} catch (error) {
					reject(new Error('上传失败'));
				}
			},
			fail: (error) => {
				reject(error);
			},
			complete: () => {
				Taro.hideLoading();
			},
		});
	});
};

/**
 * 选择并上传图片
 */
export const chooseAndUploadImage = (
	count: number = 1,
	sizeType: ('original' | 'compressed')[] = ['compressed'],
	sourceType: ('album' | 'camera')[] = ['album', 'camera'],
): Promise<UploadResponse[]> => {
	return new Promise((resolve, reject) => {
		Taro.chooseImage({
			count,
			sizeType,
			sourceType,
			success: async (res) => {
				try {
					const uploadPromises = res.tempFilePaths.map((filePath) =>
						uploadFile(filePath, 'image'),
					);
					const results = await Promise.all(uploadPromises);
					resolve(results);
				} catch (error) {
					reject(error);
				}
			},
			fail: (error) => {
				reject(error);
			},
		});
	});
};

/**
 * 通用列表查询
 */
export const getList = <T>(
	url: string,
	params: PaginationParams,
): Promise<PaginationResponse<T>> => {
	return request.get<PaginationResponse<T>>(url, params);
};

/**
 * 通用详情查询
 */
export const getDetail = <T>(url: string, id: string | number): Promise<T> => {
	return request.get<T>(`${url}/${id}`);
};

/**
 * 通用创建
 */
export const create = <T>(url: string, data: any): Promise<T> => {
	return request.post<T>(url, data, {
		showLoading: true,
		loadingText: '创建中...',
	});
};

/**
 * 通用更新
 */
export const update = <T>(url: string, id: string | number, data: any): Promise<T> => {
	return request.put<T>(`${url}/${id}`, data, {
		showLoading: true,
		loadingText: '更新中...',
	});
};

/**
 * 通用删除
 */
export const remove = (url: string, id: string | number): Promise<void> => {
	return request.delete<void>(
		`${url}/${id}`,
		{},
		{
			showLoading: true,
			loadingText: '删除中...',
		},
	);
};

/**
 * 批量删除
 */
export const batchRemove = (url: string, ids: (string | number)[]): Promise<void> => {
	return request.post<void>(
		`${url}/batch-delete`,
		{ ids },
		{
			showLoading: true,
			loadingText: '删除中...',
		},
	);
};

/**
 * 网络状态检查
 */
export const checkNetworkStatus = (): Promise<{
	isConnected: boolean;
	networkType: string;
}> => {
	return new Promise((resolve) => {
		Taro.getNetworkType({
			success: (res) => {
				resolve({
					isConnected: res.networkType !== 'none',
					networkType: res.networkType,
				});
			},
			fail: () => {
				resolve({
					isConnected: false,
					networkType: 'none',
				});
			},
		});
	});
};

/**
 * 下载文件
 */
export const downloadFile = (url: string, _filename?: string): Promise<string> => {
	return new Promise((resolve, reject) => {
		Taro.showLoading({ title: '下载中...' });

		Taro.downloadFile({
			url,
			success: (res) => {
				if (res.statusCode === 200) {
					resolve(res.tempFilePath);
				} else {
					reject(new Error('下载失败'));
				}
			},
			fail: (error) => {
				reject(error);
			},
			complete: () => {
				Taro.hideLoading();
			},
		});
	});
};

/**
 * 预览图片
 */
export const previewImage = (urls: string[], current?: string): void => {
	Taro.previewImage({
		urls,
		current: current || urls[0],
	});
};

/**
 * 保存图片到相册
 */
export const saveImageToPhotosAlbum = (filePath: string): Promise<void> => {
	return new Promise((resolve, reject) => {
		Taro.saveImageToPhotosAlbum({
			filePath,
			success: () => {
				Taro.showToast({
					title: '保存成功',
					icon: 'success',
				});
				resolve();
			},
			fail: (error) => {
				if (error.errMsg.includes('auth deny')) {
					Taro.showModal({
						title: '提示',
						content: '需要授权访问相册',
						success: (res) => {
							if (res.confirm) {
								Taro.openSetting();
							}
						},
					});
				}
				reject(error);
			},
		});
	});
};
