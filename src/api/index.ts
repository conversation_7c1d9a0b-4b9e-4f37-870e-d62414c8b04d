/**
 * API 统一导出文件
 */

// 导出请求库
export { default as request, RequestManager } from '@/libs/request';
export type { RequestConfig, ResponseData } from '@/libs/request';

// 导出配置
export * from '@/config/api';

// 导出认证相关 API
export * from './auth';

// 导出通用 API
export * from './common';

// 导出类型定义
export type {
	LoginParams,
	WechatLoginParams,
	PhoneLoginParams,
	LoginResponse,
	UserProfile,
	// PaginationParams,
	// PaginationResponse,
	// UploadResponse,
	// SystemConfig
} from './auth';

export type {
	PaginationParams as CommonPaginationParams,
	PaginationResponse as CommonPaginationResponse,
	UploadResponse as CommonUploadResponse,
	SystemConfig as CommonSystemConfig,
} from './common';
