# 网络请求库使用指南

这是一个基于 Taro.request 封装的网络请求库，提供了完整的拦截器、错误处理、认证集成等功能。

## 功能特性

### 🚀 核心功能
- ✅ 基于 Taro.request 的多端兼容
- ✅ 请求/响应/错误拦截器
- ✅ 自动认证头部添加
- ✅ 统一错误处理和提示
- ✅ 请求重试机制
- ✅ 防重复请求
- ✅ 加载状态管理

### 🔧 高级功能
- ✅ 文件上传支持
- ✅ 图片选择和上传
- ✅ 分页查询封装
- ✅ CRUD 操作封装
- ✅ 环境配置管理
- ✅ TypeScript 类型支持

## 快速开始

### 1. 基础使用

```typescript
import { request } from '@/api';

// GET 请求
const data = await request.get('/api/users');

// POST 请求
const result = await request.post('/api/users', {
  name: '张三',
  email: '<EMAIL>'
});

// PUT 请求
const updated = await request.put('/api/users/1', {
  name: '李四'
});

// DELETE 请求
await request.delete('/api/users/1');
```

### 2. 配置选项

```typescript
import { request } from '@/api';

const data = await request.get('/api/data', {
  param1: 'value1'
}, {
  showLoading: true,        // 显示加载提示
  loadingText: '加载中...', // 加载提示文字
  showError: true,          // 显示错误提示
  needAuth: true,           // 需要认证
  retry: 2,                 // 重试次数
  timeout: 10000           // 超时时间
});
```

### 3. 认证相关

```typescript
import { login, wechatLogin, getUserProfile } from '@/api';

// 用户名密码登录
const loginResult = await login({
  username: 'admin',
  password: '123456'
});

// 微信登录
const wechatResult = await wechatLogin({
  code: 'wx_code',
  userInfo: { /* 微信用户信息 */ },
  platform: 'wechat'
});

// 获取用户信息
const userInfo = await getUserProfile();
```

### 4. 文件上传

```typescript
import { uploadFile, chooseAndUploadImage } from '@/api';

// 上传指定文件
const uploadResult = await uploadFile('/temp/image.jpg', 'image');

// 选择并上传图片
const images = await chooseAndUploadImage(3); // 最多选择3张
```

### 5. 列表和 CRUD 操作

```typescript
import { getList, create, update, remove } from '@/api';

// 获取分页列表
const listData = await getList('/api/articles', {
  page: 1,
  pageSize: 10,
  keyword: '搜索关键词'
});

// 创建
const newItem = await create('/api/articles', {
  title: '标题',
  content: '内容'
});

// 更新
const updatedItem = await update('/api/articles', '1', {
  title: '新标题'
});

// 删除
await remove('/api/articles', '1');
```

## 配置说明

### 环境配置

在 `src/config/api.ts` 中配置不同环境的 API 地址：

```typescript
const configs = {
  development: {
    baseURL: 'https://dev-api.example.com',
    timeout: 10000,
    enableMock: true,
    enableLog: true
  },
  production: {
    baseURL: 'https://api.example.com',
    timeout: 8000,
    enableMock: false,
    enableLog: false
  }
};
```

### API 路径管理

在 `src/config/api.ts` 中统一管理 API 路径：

```typescript
export const API_PATHS = {
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    WECHAT_LOGIN: '/auth/wechat/login'
  },
  USER: {
    PROFILE: '/user/profile',
    UPDATE_PROFILE: '/user/profile'
  }
};
```

## 拦截器

### 请求拦截器

```typescript
import { request } from '@/api';

// 添加自定义请求拦截器
request.addRequestInterceptor((config) => {
  // 添加自定义头部
  config.header = {
    ...config.header,
    'Custom-Header': 'custom-value'
  };
  return config;
});
```

### 响应拦截器

```typescript
// 添加自定义响应拦截器
request.addResponseInterceptor((response) => {
  // 自定义响应处理
  console.log('响应数据:', response.data);
  return response;
});
```

### 错误拦截器

```typescript
// 添加自定义错误拦截器
request.addErrorInterceptor((error) => {
  // 自定义错误处理
  if (error.statusCode === 403) {
    // 处理权限错误
    Taro.showModal({
      title: '提示',
      content: '没有访问权限'
    });
  }
  throw error;
});
```

## 错误处理

### 自动错误处理

请求库会自动处理常见错误：

- **网络错误**：显示"网络连接失败"
- **超时错误**：显示"请求超时"
- **401 错误**：自动清除登录状态并跳转
- **业务错误**：显示后端返回的错误信息

### 手动错误处理

```typescript
try {
  const data = await request.get('/api/data');
} catch (error) {
  // 自定义错误处理
  console.error('请求失败:', error.message);
  
  if (error.message.includes('网络')) {
    // 网络错误处理
  } else if (error.message.includes('401')) {
    // 认证错误处理
  }
}
```

## 高级用法

### 并发请求

```typescript
const [user, articles, config] = await Promise.all([
  getUserProfile(),
  getList('/api/articles', { page: 1, pageSize: 10 }),
  request.get('/api/config', {}, { needAuth: false })
]);
```

### 请求重试

```typescript
const data = await request.get('/api/data', {}, {
  retry: 3,  // 失败后重试3次
  timeout: 5000
});
```

### 防重复请求

相同的请求（URL + 方法 + 参数）在前一个请求完成前不会重复发送。

### 自定义实例

```typescript
import { RequestManager } from '@/api';

// 创建自定义请求实例
const customRequest = new RequestManager('https://custom-api.com');

customRequest.setDefaultConfig({
  timeout: 15000,
  showLoading: false
});
```

## 类型定义

### 请求配置

```typescript
interface RequestConfig {
  showLoading?: boolean;     // 显示加载提示
  loadingText?: string;      // 加载提示文字
  showError?: boolean;       // 显示错误提示
  needAuth?: boolean;        // 需要认证
  retry?: number;            // 重试次数
  timeout?: number;          // 超时时间
}
```

### 响应数据

```typescript
interface ResponseData<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}
```

### 分页参数

```typescript
interface PaginationParams {
  page: number;
  pageSize: number;
  keyword?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
```

## 最佳实践

1. **统一错误处理**：让拦截器处理通用错误，业务代码只处理特定错误
2. **合理使用加载提示**：避免频繁的加载提示影响用户体验
3. **类型安全**：使用 TypeScript 类型确保 API 调用的正确性
4. **环境配置**：根据不同环境使用不同的 API 配置
5. **错误重试**：对于网络不稳定的情况，适当使用重试机制

## 常见问题

### Q: 如何处理文件上传？
A: 使用 `uploadFile` 或 `chooseAndUploadImage` 方法。

### Q: 如何添加自定义头部？
A: 通过请求拦截器或在请求时传入 `header` 参数。

### Q: 如何处理不同环境的 API 地址？
A: 在 `src/config/api.ts` 中配置不同环境的 `baseURL`。

### Q: 如何禁用某个请求的错误提示？
A: 设置 `showError: false`。

这个网络请求库为你的 Taro 项目提供了完整、可靠的网络请求解决方案！
