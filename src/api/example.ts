/**
 * API 使用示例
 * 展示如何使用封装的网络请求库
 */

import {
	request,
	login,
	wechatLogin,
	getUserProfile,
	updateUserProfile,
	// uploadFile,
	chooseAndUploadImage,
	getList,
	create,
	update,
	remove,
} from '@/api';

// 示例：用户登录
export const exampleLogin = async () => {
	try {
		const response = await login({
			username: 'admin',
			password: '123456',
		});

		console.log('登录成功:', response);
		return response;
	} catch (error) {
		console.error('登录失败:', error);
		throw error;
	}
};

// 示例：微信登录
export const exampleWechatLogin = async (code: string, userInfo: any) => {
	try {
		const response = await wechatLogin({
			code,
			userInfo,
			platform: 'wechat',
		});

		console.log('微信登录成功:', response);
		return response;
	} catch (error) {
		console.error('微信登录失败:', error);
		throw error;
	}
};

// 示例：获取用户信息
export const exampleGetUserProfile = async () => {
	try {
		const userInfo = await getUserProfile();
		console.log('用户信息:', userInfo);
		return userInfo;
	} catch (error) {
		console.error('获取用户信息失败:', error);
		throw error;
	}
};

// 示例：更新用户信息
export const exampleUpdateProfile = async () => {
	try {
		const updatedInfo = await updateUserProfile({
			nickname: '新昵称',
			signature: '这是我的个性签名',
		});

		console.log('更新成功:', updatedInfo);
		return updatedInfo;
	} catch (error) {
		console.error('更新失败:', error);
		throw error;
	}
};

// 示例：选择并上传图片
export const exampleUploadImage = async () => {
	try {
		const results = await chooseAndUploadImage(1);
		console.log('上传成功:', results);
		return results[0];
	} catch (error) {
		console.error('上传失败:', error);
		throw error;
	}
};

// 示例：获取列表数据
export const exampleGetList = async () => {
	try {
		const listData = await getList('/api/articles', {
			page: 1,
			pageSize: 10,
			keyword: '搜索关键词',
		});

		console.log('列表数据:', listData);
		return listData;
	} catch (error) {
		console.error('获取列表失败:', error);
		throw error;
	}
};

// 示例：创建数据
export const exampleCreate = async () => {
	try {
		const newItem = await create('/api/articles', {
			title: '文章标题',
			content: '文章内容',
			tags: ['标签1', '标签2'],
		});

		console.log('创建成功:', newItem);
		return newItem;
	} catch (error) {
		console.error('创建失败:', error);
		throw error;
	}
};

// 示例：更新数据
export const exampleUpdate = async (id: string) => {
	try {
		const updatedItem = await update('/api/articles', id, {
			title: '更新后的标题',
			content: '更新后的内容',
		});

		console.log('更新成功:', updatedItem);
		return updatedItem;
	} catch (error) {
		console.error('更新失败:', error);
		throw error;
	}
};

// 示例：删除数据
export const exampleDelete = async (id: string) => {
	try {
		await remove('/api/articles', id);
		console.log('删除成功');
	} catch (error) {
		console.error('删除失败:', error);
		throw error;
	}
};

// 示例：自定义请求
export const exampleCustomRequest = async () => {
	try {
		// 使用 request 实例进行自定义请求
		const response = await request.get(
			'/api/custom',
			{
				param1: 'value1',
				param2: 'value2',
			},
			{
				showLoading: true,
				loadingText: '自定义加载中...',
				needAuth: false,
				retry: 2,
				timeout: 15000,
			},
		);

		console.log('自定义请求成功:', response);
		return response;
	} catch (error) {
		console.error('自定义请求失败:', error);
		throw error;
	}
};

// 示例：并发请求
export const exampleConcurrentRequests = async () => {
	try {
		const [userInfo, listData, systemConfig] = await Promise.all([
			getUserProfile(),
			getList('/api/articles', { page: 1, pageSize: 5 }),
			request.get('/api/system/config', {}, { needAuth: false }),
		]);

		console.log('并发请求结果:', {
			userInfo,
			listData,
			systemConfig,
		});

		return { userInfo, listData, systemConfig };
	} catch (error) {
		console.error('并发请求失败:', error);
		throw error;
	}
};

// 示例：错误处理
export const exampleErrorHandling = async () => {
	try {
		// 这个请求会失败
		await request.get('/api/nonexistent');
	} catch (error) {
		// 错误会被拦截器处理，并显示用户友好的提示
		console.log('捕获到错误:', error.message);

		// 可以根据错误类型进行不同处理
		if (error.message.includes('网络')) {
			console.log('网络错误，建议重试');
		} else if (error.message.includes('401')) {
			console.log('需要重新登录');
		} else {
			console.log('其他错误');
		}
	}
};
