import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { setupRequest } from '@/libs/request-setup';
import { initStore, useUserStore } from '@/stores';
import { IconFont } from '@nutui/icons-vue-taro';
import Taro from '@tarojs/taro';
import './app.scss';

const App = createApp({
	onLaunch(options) {
		console.log('🚀 应用启动', options);

		// 从本地LocalStorage读取初始化pinia中的数据 token和userInfo
		initStore();
		// 初始化请求库
		setupRequest();

		// 检查登录状态并进行页面跳转
		setTimeout(() => {
			const userStore = useUserStore();
			const isLoggedIn = userStore.isLogin();

			console.log('检查登录状态:', isLoggedIn);

			if (isLoggedIn) {
				// 已登录，跳转到首页
				Taro.switchTab({
					url: '/pages/index/index'
				});
			}
			// 未登录则保持在登录页面（默认页面）
		}, 100);
	},
	onShow(options) {
		console.log('📱 应用显示', options);
	},
	onHide() {
		console.log('📱 应用隐藏');
	},
	// 入口组件不需要实现 render 方法，即使实现了也会被 taro 所覆盖
});

App.use(createPinia()).component('IconFont', IconFont);

export default App;
