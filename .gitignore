.DS_Store
node_modules
/dist


# local env files
.env.local
.env.*.local

# Log files
debug.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# lock files
yarn.lock
package-lock.json
*.lock

# yarn3 igonre
.yarn/*
!.yarn/cache
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.min.js

#rar
*.rar
*.zip
*.tar
*.gz
*.7z
pnpm-lock.yaml

# 忽略 project.private.config.json 文件，属于私人配置
project.private.config.json
